# 增强版Swagger转Markdown功能

## 改进内容

### 1. **POJO参数类型解析**
- 自动识别body参数中的POJO类型
- 显示POJO类的详细字段信息
- 包含字段类型、是否必填、描述等

### 2. **响应类型详细解析**
- 解析200状态码的响应对象
- 显示响应对象的字段详情
- 支持嵌套对象和数组类型

### 3. **增强的类型识别**
- 支持基本类型 + 格式（如：integer(int64)）
- 支持数组类型（如：Array<String>）
- 支持枚举类型显示
- 支持引用类型解析

## 示例输出

### API接口示例

#### POST /1.0/order/create

**摘要**: 创建订单

**标签**: 订单操作

**参数**:

| 参数名 | 位置 | 类型 | 必填 | 描述 |
|--------|------|------|------|------|
| request | body | 订单创建 | 是 | request |

**订单创建 字段详情**:

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderMainInfo | 订单主信息 | 是 | 订单主信息 |
| orderDetailInfoList | Array<订单明细信息> | 是 | 订单明细信息 |
| orderPayInfo | 订单支付信息 | 否 | 订单支付信息 |
| orderReceiveInfo | 订单收货信息 | 否 | 订单收货信息 |
| orderUserInfo | 订单用户信息 | 否 | 订单用户信息 |

**响应**:

| 状态码 | 描述 | 数据类型 |
|--------|------|----------|
| 200 | OK | ResponseBase«Void» |

**ResponseBase«Void» 响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 是否成功 |
| data | Object | 响应数据 |
| message | string | 响应消息 |
| code | string | 响应码 |

---

## 技术特性

### 1. **深度解析**
- 递归解析POJO类的所有字段
- 自动识别必填字段（required数组）
- 支持复杂嵌套结构

### 2. **类型映射**
- `string` → String
- `integer(int32)` → Integer
- `integer(int64)` → Long
- `number` → Number
- `boolean` → Boolean
- `array` → Array<T>
- `object` → Object
- `$ref` → 具体类名

### 3. **智能显示**
- 只对body参数显示详细字段
- 只对200响应显示详细字段
- 避免信息冗余

## 使用效果

现在当您调用API时：

```json
POST /knowledge/start-pull-swagger
{
  "knowledgeGroupId": 1,
  "swaggerUrl": "http://order-service.svc.k8s.test.hxyxt.com/v2/api-docs"
}
```

生成的Markdown文档将包含：

1. **完整的参数信息** - 不仅显示参数类型，还显示POJO类的所有字段
2. **详细的响应结构** - 清楚地知道返回数据的结构
3. **类型映射** - 准确的Java类型对应关系
4. **必填标识** - 明确哪些字段是必填的

这样开发者就能：
- 快速了解API的完整结构
- 知道需要传递哪些字段
- 了解响应数据的格式
- 正确构造请求参数
