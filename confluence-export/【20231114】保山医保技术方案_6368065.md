# 【20231114】保山医保技术方案

**PRD：保山医保PRD 原型图 东软微信医保对接插件 微信授权功能**

# 一、 背景

## 1.1 业务背景

**当前云南保山市相关部门要求对应药店企业完善在线医保支付能力，我方需要积极响应，现支付宝商城小程序已经进行对接，微信商城小程序也需要进行对接保持一致，尽量在小程序一期上线时需要有此在线医保支付能力。**

## 1.2 痛点分析 todo

1.xxx

xxxxx

## 1.3 系统现状 todo

 目前没有的。

# 二、需求分析

## 2.1 业务流程 todo



![流程图](./resources/流程图.png)



## 2.2 需求功能点todo

| 功能模块 | 功能点 | 功能描述 | 所属系统 | 优先级 |
| --- | --- | --- | --- | --- |
| 任务类型管理 | 查看任务类型列表 | 可以看到系统内可支持的任务类型，且可以启用和禁用 | 管理后台 | P0 |
| 任务管理 | 任务列表查询 | 展示任务列表的数据 | 管理后台 | P0 |
| 新增任务 | 新增收集表任务 | 管理后台 | P0 |
| 发布、撤回、结束、编辑任务 | 任务状态的流转变更 | 管理后台 | P0 |
| 查看任务列表详情 | 可跳转到查看任务详情页面 | 管理后台 | P0 |
| 任务概览统计 | 统计当前任务执行的情况 | 管理后台 | P1 |
| 查看任务统计 | 查看任务统计的数据 | 管理后台 | P1 |
| 导出门店完成详情 | 导出参与门店的详情数据 | 管理后台 | P1 |
| 待办任务列 | 任务类型筛选 | 可选择不同类型的任务进行任务的筛选 | APP | P1 |
| 待办任务展示 | 展示当前已经发布未过期/完成的任务 | APP | P0 |
| 待办任务过期时间计算 | 根据任务截止日期-当前日期计算任务到期时间 | APP | P0 |
| 处理/查看任务 | 点击按钮可跳转到任务详情 | APP | P0 |
| 任务记录 | 任务状态筛选 | 可选择不同状态进行任务筛选 | APP | P1 |
| 待办任务展示 | 展示已完成/已过期的任务 | APP | P0 |
| 任务完成标签展示 | 可展示本人完成/他人完成的标签 | APP | P0 |
| 查看任务 | 点击任务卡片可进入到任务详情 | APP | P0 |
| 任务详情 | 任务详情页面 | 展示任务的详情 | APP | P0 |
| 填写表单 | 根据表单模版，填写输入文本、选择单选、多选等 | APP | P0 |
| 提交 | 可对收集表进行提交 | APP | P0 |
| 任务提醒推送 | 任务提醒推送 | 新任务消息提示、临期任务消息提示可做下拉栏通知框提醒、横幅提醒 | APP | P0 |


# 三、目标 todo

## 3.1 本期目标

### 3.1.1 业务目标

- 任务管理：提供创建、分配、编辑和删除任务的功能。管理人员可以在任务中台上创建任务，并指派给特定的成员或门店。任务可以设置启止日期和其他属性。
- 进度跟踪：任务中台可以实时显示任务的进度和状态。管理人员可以查看任务的完成情况，了解任务的进展，并及时调整和分配资源。
- 通知和提醒：任务中台可以通过应用内通知等方式提醒店员任务的截止日期、变更或重要更新。


### 3.1.2 技术目标

初期技术目标是建立一个稳定、可靠的任务中台系统，并满足以下方面的需求：

- 可扩展性：系统应该能够支持大量的任务和用户，并具备水平扩展及功能扩展的能力，以应对未来的增长和需求。
- 监控和日志：系统应该具备完善的监控和日志功能，能够实时监控系统的运行状态、性能指标和异常情况，并记录关键操作和事件的日志，方便故障排查和系统优化。
- 安全性和权限管理：任务中台系统可能涉及到敏感数据和操作，因此需要具备良好的安全性和权限管理机制。可以采用身份认证、访问控制等技术来保护系统的安全性，确保只有授权用户能够访问和操作相关数据和功能。


## 3.2 中长期目标

搭建任务中台系统的中长期目标是建立一个稳定、高效、可扩展的任务管理平台，能够满足不断增长的任务管理需求和用户规模。

# 四、整体设计 todo

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
| 任务中台 | 任务中台服务是指一个集中管理、分配和监控任务的平台或系统。它可以帮助组织或团队更好地组织和管理任务，提高工作效率和协作能力。 |
| 任务 | 一个需要完成的工作或活动，包含起止时间、执行内容、执行对象等信息。 |
| 任务项 | 任务下分到每个门店或个人的子任务叫做任务项。 |
| 任务模板 | 是对某一类任务场景的共性的抽象，这类任务应具有相同的**展示、操作逻辑**以及任务**完成判定逻辑**，通过预先定义好的任务格式和执行流程，用于指导和规范任务的执行，同时可以帮助管理者快速创建任务。 |
| 通用模板 | 一些没有业务逻辑的任务，建立通用的任务模板，这样各个接入方都可以直接使用。 |
| 执行计划 | 是指根据任务配置将任务拆分为多个步骤，步骤包含执行时间和执行内容，任务根据这些步骤**有序**推进任务的进行。执行计划可分为：分配、开始、通知、结束。 |
| 执行人 | 需要他来完成或参与任务执行的人。 |
| 完成人 | 参与或完成任务的人。 |
| 执行明细 | 执行人需要执行的具体明细，如收集表则是需要填报的表单、退货任务则是门店需要下架的商品。 |
|  |  |


## 4.2 流程图 todo

### 4.2.1 系统流程全景图

**1、任务中台（运营端）：系统生成待办任务 & 消息通知流程**



![生成待办任务及消息通知流程](./resources/生成待办任务及消息通知流程.png)



**2、任务中台（App端）：用户执行任务流程**

**

![任务执行任务](./resources/任务执行任务.png)

**

### 4.2.2模型图

**1.任务状态流转**



![任务状态流转](./resources/任务状态流转.png)



**2. 四色原型图**

**

![模型图](./resources/模型图.png)

**

**3. 领域模型**

**

![领域模型](./resources/领域模型.png)

**

## 4.3 用例图

**

![任务管理用例](./resources/任务管理用例.png)

**

## 4.4 ER图

**1、ER模型设计**

**详见：存储数据库设计**



![修改](./resources/修改.png)



**2、核心问题思考**

| 序号 | 问题描述（Q） | 问题解答 / 解决方案（A） |
| --- | --- | --- |
| 1 | 任务中台现在用固定的6~7套模板，解决短中期业务问题。后续有模板动态配置需求时，现有的模型可以支持吗？ | **不支持任务模板动态配置。**通过与产品同学沟通，任务模板带有业务属性，纯粹的动态表单配置很难满足实际业务需求。因此后续需要通过前后端定制化开发来做模板的新增操作。 |
| 2 | APP端用户查看待办任务时，后端接口返回数据的逻辑是什么？ | 根据任务ID查询任务模板配置和任务项执行明细，模板配置中保存有前端需要展示内容，执行明细中保存的是当前执行人需要执行的内容（如表单、需要下架的商品）。 |
| 3 | “任务模板关联的表单”存在变更诉求，系统如何规避表单变更带来的风险？ | 表单在被应用后则不可以修改问题和选项描述，但可以通过新增选项和隐藏旧选项实现变更，这样可以保证已填写的表单内容不会出现异议。 |
| 4 | 任务可以溯源到创建任务时，使用的表单吗？ | **任务模板-表单关联表记录创建任务时使用的表单版本号。**通过表单ID+表单版本号可以溯源到任务在创建时使用的表单信息。- 表单结果需要记录表单的版本号 |


## 4.5 架构图

逻辑架构图

**

![逻辑架构](./resources/逻辑架构.png)

**

## 五、详细设计

## 5.1 详细模块设计



![123](./resources/123.png)



### 5.1.1 准备阶段

1. 准备模板


- 什么是任务模板


是对某一类任务场景的共性的抽象，这类任务应具有相同的**展示、操作逻辑**以及任务**完成判定逻辑**，通过预先定义好的任务格式和执行流程，用于指导和规范任务的执行，同时可以帮助管理者快速创建任务。这些包含业务逻辑的内容则放在各接入方自己实现，任务中台在执行过程中通过SPI的方式调用执行这些业务逻辑。



![SPI](./resources/SPI.png)



任务模板包含以下信息：

- 任务大类：门店任务/个人任务
- 任务详情页：接入方实现任务详情页地址
- 所属业务方：通用/业务系统
- 回调接口信息：业务实现的服务信息




![模板的作用](./resources/模板的作用.png)



|  | 问题 |  |
| --- | --- | --- |
| 1 | 业务接入时如何服务鉴权 | - 模板中维护了所属业务方，同一个业务方只能创建自己业务的模板或通用模板任务 - 业务接入需要定义业务标识,通过对业务标识授权 - 服务调用鉴权可以暂时不增加 |
|  |  |  |


### 5.1.2 创建任务

#### 5.1.2.1 创建任务

创建任务是任务配置信息确认的过程，配置信息主要是以下信息组成：

- 基础信息：包含任务名、创建方式、创建渠道、开始时间、结束时间、所属部门、创建人等信息
- 任务模板：创建时刻的模板快照，以及模板相关参数（例如：收集表模板的模板参数就是绑定的表单收集应用编号）
- 任务通知计划：包含通知时间、通知模板、通知条件
- 任务执行范围：保存的是运营选择的任务执行对象的查询条件，会有如下条件：
  - 组织范围
  - 员工/门店名单
  - 员工/门店黑名单
- 组织范围
- 员工/门店名单
- 员工/门店黑名单


**外部依赖：**

- 组织架构查询接口(用code还是id？)


javaMidnighttrue    @ApiOperation(
            value = "查询",
            notes = "组织机构树,适用于树型展示",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/searchOrgTreeByPid")
    public ResponseBase<List<OrgNodeDTO>> queryOrgTreeByPid(@Valid @RequestBody QueryOrgTreeDTO queryOrgTreeDTO, BindingResult result) throws Exception {
        this.checkValid(result);
        List<OrganizationResDTO> list = departmentService.queryOrgTreeByCondition(queryOrgTreeDTO);
        List<OrgNodeDTO> orgNodeDTOS = BeanUtil.copyList(list, OrgNodeDTO.class);
        return generateSuccess(orgNodeDTOS);
    }

    @ApiOperation(
            value = "多条件查询",
            notes = "组织机构树,适用于组织机构查询列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_search")
    public ResponseBase<List<OrganizationResDTO>> queryOrgInfoByCondition(@Valid @RequestBody QueryOrgTreeDTO queryOrgTreeDTO, BindingResult result) throws Exception {
        this.checkValid(result);
        return generateSuccess(departmentService.queryOrgTreeByCondition(queryOrgTreeDTO));
    }
    @ApiOperation(
            value = "多条件查询-分页",
            notes = "适用于组织机构查询列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_searchPage")
    public ResponseBase<PageDTO<OrganizationResDTO>> queryOrgPageByCondition(@Valid @RequestBody QueryOrgPageDTO queryOrgTreeDTO, BindingResult result) throws Exception {
        this.checkValid(result);
        return generateSuccess(departmentService.queryOrgPageByCondition(queryOrgTreeDTO));
    }
    @ApiOperation(
            value = "多条件查询-分页",
            notes = "前端公共组件专用",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_searchForFrontCommonModule")
    public ResponseBase<PageDTO<OrganizationResDTO>> searchForFrontCommonModule(@Valid @RequestBody QueryOrgForFrontCommonPageDTO queryOrgTreeDTO, BindingResult result) throws Exception {
        this.checkValid(result);
        return generateSuccess(departmentService.searchForFrontCommonModule(queryOrgTreeDTO));
    }

    @ApiOperation(
            value = "多条件查询-树",
            notes = "前端公共组件专用",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_searchTreeForFrontCommonModule")
    public ResponseBase<List<OrganizationResDTO>> searchTreeForFrontCommonModule(@RequestBody QueryOrgForFrontCommonPageDTO queryOrgTreeDTO) {
        return generateSuccess(departmentService.searchTreeForFrontCommonModule(queryOrgTreeDTO));
    }

    @ApiOperation(value = "根据用户id，查询用户可查看的机构和授权门店", notes = "根据用户id，查询用户可查看的机构和授权门店")
    @PostMapping("/_queryUserOrganization")
    public ResponseBase<List<UserOrganizationRspDTO>> queryUserOrganizationByUser(@Valid @RequestBody UserOrganizationReqDTO dto, BindingResult result) {
        this.checkValid(result);
        return this.generateSuccess(departmentService.queryUserOrganizationByUser(dto));
    }

- 根据组织架构的店员查询接口（最好是游标查询）


javaMidnighttrue    @ApiOperation(value = "根据商户编码和机构列表查询门店下所有在职的员工", notes = "根据商户编码和机构列表查询门店下所有在职的员工")
    @PostMapping("queryEmpByOrgCodes")
    public ResponseBase<PageDTO<QueryEmpByStoresRspDTO>> queryEmpByOrgCodes(@RequestBody QueryEmpDTO reqDTO) {
        return generateSuccess(employeeService.queryEmpByOrgCodes(reqDTO));
    }



- 根据组织架构的门店查询接口


javaMidnighttrue    @ApiOperation(value = "根据机构ID，查询机构下所有门店", notes = "查询机构下所有门店")
    @PostMapping("/_queryOrgSubStoreList")
    public ResponseBase<PageDTO<StoreResDTO>> queryOrgSubStoreList(@Valid @RequestBody SubStoreDTO subStoreDTO, BindingResult result) {
        this.checkValid(result);
        return this.generateSuccess(storeService.queryOrgSubStoreList(subStoreDTO));
    }

    @ApiOperation(
            value = "门店多条件查询",
            notes = "多条件查询，返回上线门店",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/queryStoreByOrgId")
    public ResponseBase<PageDTO<StoreResDTO>> queryStoreByOrgId(@Valid @RequestBody QueryStoreByOrgIdDTO queryStoreByOrgIdDTO, BindingResult result) {
        this.checkValid(result);
        return this.generateSuccess(storeService.queryStoreByOrgId(queryStoreByOrgIdDTO));
    }

- 根据empId查询员工基本信息（是否可以查询到分公司信息）


|  | 问题 |  |
| --- | --- | --- |
| 1 | 分页查询可能存在深翻页和数据不一致的问题 | 需要调研查询接口性能，最好时可以改成游标查询 |
| 2 | 所属公司、大区需要有判断逻辑（产品需要公司和大区的用意？） | [组织机构取数规则及问题答复 - 产品部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6357081) |


### 5.1.3 任务发布

RDarktruetrue@startuml
actor       管理员       as actor
participant    任务中台管理端 as taskFront
participant 任务域       as task
participant 业务域or通用模板  as tempStrategy
participant 表单域  as form
database    数据库    as db
queue       MQ    as mq
actor -> taskFront : 发布任务
autonumber 1
taskFront -> task : 发布
activate task
task -> task : 权限校验
task -> db : 查询当前任务配置
task -> task : 基础信息校验
task -> db : 修改任务状态为发布中
task -> tempStrategy :执行各模板发布逻辑
group 通用模板/业务方实现 [收集表发布逻辑]
activate tempStrategy
tempStrategy -> tempStrategy : 模板配置信息校验
tempStrategy -> form : 创建表单应用项
form --> tempStrategy : 应用编号
tempStrategy -> tempStrategy : 更新模板配置,保存表单应用编号
end
tempStrategy --> task :
deactivate tempStrategy
task -> task : 生成执行计划
task -> mq : 发布领域事件
task --> actor : 返回任务在发布中
deactivate task
@enduml

执行计划生成示意图：



![生成执行流程](./resources/生成执行流程.png)



|  | 问题 |  |
| --- | --- | --- |
| 1 | 哪些基础信息校验？ | 必填参数检验、当前是否已过发布时间（开始时间应在发布时间10分钟后，预留生成任务项和执行人的时间）。 |
| 2 | 在参数校验后如果被更新参数？ | 更新发布中状态时有版本号校验，版本不一致则驳回。 |
| 3 | 模板参数校验失败，发布状态回滚 | 可以放在同一个事务中，模板发布逻辑异常自动回滚发布状态。 |
| 4 | 执行计划有哪些？ | 目前有分配计划、开始计划、通知计划、结束计划；后期可根据需求增加或调整。|  | 执行计划 |  | 前置条件 | | --- | --- | --- | --- | | 1 | 分配任务 | 生成任务项、执行人、和执行明细的过程 | 任务状态=发布中 | | 2 | 开始任务 | 任务开始状态变更 | 任务状态=待生效、结束时间>当前时间>=开始时间 + 前置分配计划执行完成 | | 3 | 通知任务 | 给执行人推送消息 | 任务状态在进行中(待生效、进行中) + 前置分配计划执行完成 | | 4 | 结束任务 | 任务结束状态变更 | 任务状态!=已结束 + 当前时间>结束时间 | |  | 执行计划 |  | 前置条件 | 1 | 分配任务 | 生成任务项、执行人、和执行明细的过程 | 任务状态=发布中 | 2 | 开始任务 | 任务开始状态变更 | 任务状态=待生效、结束时间>当前时间>=开始时间 + 前置分配计划执行完成 | 3 | 通知任务 | 给执行人推送消息 | 任务状态在进行中(待生效、进行中) + 前置分配计划执行完成 | 4 | 结束任务 | 任务结束状态变更 | 任务状态!=已结束 + 当前时间>结束时间 |
|  | 执行计划 |  | 前置条件 |
| 1 | 分配任务 | 生成任务项、执行人、和执行明细的过程 | 任务状态=发布中 |
| 2 | 开始任务 | 任务开始状态变更 | 任务状态=待生效、结束时间>当前时间>=开始时间 + 前置分配计划执行完成 |
| 3 | 通知任务 | 给执行人推送消息 | 任务状态在进行中(待生效、进行中) + 前置分配计划执行完成 |
| 4 | 结束任务 | 任务结束状态变更 | 任务状态!=已结束 + 当前时间>结束时间 |
| 5 | 门店人员发生变动怎么办？ | 本期暂不根据门店人员变动动态更新任务分配情况，后续可以监听人员状态/岗位变更消息来调整任务。 |


### 5.1.4 执行阶段

#### ******* 服务端

在执行阶段，服务端主要工作是按照执行流程推动任务执行，这里采用定时任务轮询执行流程表的方式。具体的流程如何执行通过定义执行动作策略来实现。

RDarktruetrue@startuml
participant 定时任务 as timer
participant 任务域 as task
participant 执行计划策略 as strategy
participant 业务域 as template
participant 消息中台 as message
participant 心云基础服务 as xy
database    数据库 as db
queue       MQ    as mq
autonumber 1
timer -> task : 每1分钟调度
activate task
task -> db : 拉取达到执行时间未执行的计划
db --> task :
task -> db : 更新计划状态为执行中
task -> task: 根据执行时间排序
group loop [遍历执行计划]
task -> strategy:根据策略类型调用执行策略
activate strategy
strategy -> strategy:状态校验
strategy -> strategy:执行策略
strategy --> task:执行结果
deactivate strategy
alt 执行成功
task -> db:更新计划状态为已完成
else 执行失败
task -> strategy:Retry(间隔1秒重试3次)
else 重试失败
task -> task:打印错误日志
task -> db:更新执行计划状态为失败
end
end
task --> timer:执行完成
deactivate task
== 分配策略执行 ==
autonumber 1
task -> strategy:执行分配策略
activate strategy
strategy -> db:获取任务信息
db --> strategy:
strategy -> strategy:判断任务状态是发布中
strategy -> db:删除已有任务项和执行人
loop 分页查询门店/人员
alt 门店任务
strategy -> xy:根据配置条件拉取门店
strategy -> xy:根据门店拉取员工
strategy -> strategy:判断门店下员工不为空
else 个人任务
strategy -> xy:根据配置条件拉取员工
end
strategy -> template:
activate template
template -> template:生成执行明细
template -> template:生成任务项
template -> template:生成执行人
template --> strategy
deactivate template
end
strategy -> db : 保存执行明细、任务项、执行人
strategy -> db : 修改任务状态为待生效
strategy -> mq : 发布领域事件
strategy --> task
deactivate strategy

== 通知策略执行 ==
autonumber 1
task -> strategy:执行通知策略
activate strategy
strategy -> db:获取任务信息
strategy -> strategy:判断任务是否在进行中状态
loop
strategy -> db:获取任务执行人
strategy -> xy: 判断账号有效
strategy -> db:查询当前执行人本次执行计划是否有通知记录
strategy -> strategy:根据模板动态生成消息内容
strategy -> message:发送消息
strategy -> db:记录通知记录
end
strategy -> mq : 发布领域事件
strategy --> task:成功
deactivate strategy

== 开始/结束策略执行 ==
autonumber 1
task -> strategy:执行开始策略
activate strategy
strategy -> db:获取任务信息
strategy -> strategy:判断任务是否在带生效状态\n并且到达开始时间，未超过结束时间
strategy -> db:更新任务状态
strategy -> mq : 发布领域事件
strategy --> task:成功
deactivate strategy
@enduml

思考：

|  | 问题 |  |
| --- | --- | --- |
| 1 | 拉取执行计划SQL增加开始时间范围校验 | 查询sql核心条件应该”action_time < now() and status = ‘未完成’ “，随着这个表的数据累计会出现历史计划属于远大于将来计划的情况，会出现查全表的情况，所以需要给action_time字段加索引的同时，限制查询最早范围，如当前时间-24；（具体时间可以配置在配置中心，可以支持动态调整） |
| 2 | 分配效率 | 分配效率依赖心云查询接口效率，如果查询比较耗时，可以增加本地缓存优化查询效率。 |
| 3 | 通知效率 | 可以通过多线程发消息，但需要考虑消息接口的性能。//消息中台提供接口还是MQ交互 |
| 4 | 如何保证有序 | 1. 根据执行时间，2.不同执行计划类型有不同的状态判断+前置执行计划检查 |
| 5 | 执行计划的时效 | 目前执行计划的时效取决域定时任务调度周期，暂定1分钟，后续可以考虑提前30分钟将执行计划放到延迟队列中等方式优化时效。 |
| 6 | 重试幂等 | 分配：有状态校验；2. 分配会清空任务项和执行人信息（或者查询补充）通知：有通知记录表判断该次执行计划是否被执行（确保消息不回重复发送）开始/结束：状态校验+时间 |
| 7 | 保障执行计划失败，任务状态一定要回滚 | 任务状态变更一定要在事务中 |
| 8 | 如果任务执行计划积压怎么办 | 1. 消费能力不足导致的积压：分布式定时任务（多机器消费），多线程消费，优化处理逻辑 2. 正常服务重启导致的短暂积压：平滑部署 |
| 9 | 需要注意员工数据存在T+2的延迟 |  |
|  |  |  |


#### 5.1.4.2 用户端

针对本次收集表的需求，用户的完成操作为用户填写收集表内容并提交。



![收集表完成流程](./resources/收集表完成流程.png)



### 5.1.5 基础能力

#### 5.1.5.1 权限校验

- 菜单权限：心云平台提供的角色管理已经实现菜单权限的管理。
- 功能权限：主要基于心云平台提供的角色管理能力来实现功能权限的管理。
- 数据权限：这里主要通过菜单权限+操作人所属组织部门（不同数据判断层级不一样）来判断数据权限（特殊流程：设置白名单，如果该员工在白名单中则可以不受部门限制）
- 
- 

![数据权限](./resources/数据权限.png)


- 权限获取接口：javaDJango@RestController
@RequestMapping(value = "/${api.version}/res")
@Api(value = "资源管理", description = "资源管理控制器")
public class ResourceController extends AbstractController {   
 @ApiOperation(value = "资源查询", notes = "根据类型和id查询有效的资源信息资源\n" +
            "type = pkg: 传入套餐id查询套餐资源\ntype = mer: 传入商户id查询商户有效资源\n" +
            "type = user: 传入用户id查询用户有效资源\ntype = role: 传入角色id查询角色有效资源\n" +
            "type = all: 查询系统所有资源\ntype = all-p: 查询系统所有界面资源\n" +
            "type = all-d: 查询系统所有数据资源\ntype = user-p: 传入用户id查询用户界面资源\n"
            + "type = user-d: 传入用户id查询用户数据资源")
    @GetMapping("/{type}/{id}")
    public ResponseBase<List<ResourceDTO>> queryResourceForPackage(@PathVariable String type, @PathVariable String id);
}


#### ******* 操作日志

通过消费领域事件异步生成操作日志

**操作日志核心字段：**

1. 操作时间
2. 操作人ID
3. 操作对象类型：任务、任务项、表单
4. 操作对象ID：任务编号、任务项ID、表单编号
5. 操作类型：创建、编辑、删除、发布、撤回
6. 操作描述：操作动作描述，用于页面展示
7. 关联信息：操作的关联信息


## 5.2 存储数据库设计

| 序号 | 表名称 | DML |
| --- | --- | --- |
| 1 | 任务表：task | task``` CREATE TABLE `task_info` (     `id`                     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键ID',     `task_no`                varchar(64)   NOT NULL COMMENT '任务编号',     `task_category`          smallint(4)   NOT NULL COMMENT '任务种类枚举 0:门店任务 1:个人任务',     `template_no`            varchar(64)   NOT NULL COMMENT '模板编号',     `task_status`            smallint(4)   NOT NULL DEFAULT 0 COMMENT '任务状态枚举 0:未发布 1:已撤回 2:发布中 3:待生效 4:进行中 5:已结束',     `task_title`             varchar(512)  NOT NULL DEFAULT '' COMMENT '任务标题(名称）',     `task_desc`              varchar(1024) NOT NULL DEFAULT '' COMMENT '任务说明(描述)',     `task_start_time`        datetime      NOT NULL COMMENT '任务日期范围开始时间',     `task_end_time`          datetime      NOT NULL COMMENT '任务日期范围结束时间',     `task_published_time`    datetime COMMENT '任务发布时间',     `task_actual_start_time` datetime COMMENT '任务真实开始时间（任务真正开启和结束时间不一定与任务日期范围一致）',     `task_actual_end_time`   datetime COMMENT '任务真实结束时间',     `org_id`                 varchar(64)   NOT NULL COMMENT '创建人所属ORG ID',     `executor_org_path`      varchar(256)  NOT NULL COMMENT '执行人-员工所属ORG PATH',     `created_type`           smallint(4)   NOT NULL COMMENT '创建方式 1:自建 2:新增计划 3:开放接口',     `created_biz`            smallint(4)   NOT NULL COMMENT '创建业务 1:任务中台 2:POS ...', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), UNIQUE KEY `uk_taskNo` (`task_no`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '任务主表'; ``` |
| 2 | 任务项表：task_item | task_item``` CREATE TABLE `task_item` (     `id`                    bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `task_item_no`       varchar(64) NOT NULL COMMENT '任务项编号',     `task_category`         smallint(4)  NOT NULL COMMENT '任务对应的执行对象类型，1=员工、2=门店',     `task_item_object`      varchar(64)  NOT NULL COMMENT '任务项执行对象，如果是[员工]则为默认设置成员工编号、如果是[门店]则为门店id',     `task_item_object_name` varchar(512) NOT NULL COMMENT '任务项执行名称，[员工]员工名称，[门店]门店名称',     `task_status`           smallint(4)  NOT NULL COMMENT '任务执行状态枚举 0：未开始，1：未完成，2：已完成',     `operate_time`          datetime COMMENT '操作时间', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), UNIQUE KEY `uk_task_id_obj` (`task_id`, `task_item_object`, `task_category`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务项表'; ``` |
| 3 | 任务配置表：task_config | task_config``` CREATE TABLE `task_config` (     `id`           bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `config_type`  smallint(4) NOT NULL COMMENT '配置类型 10=任务对象配置，20=通知配置，30=创建任务的配置快照',     `config_value` text COMMENT '配置值，JSON格式',     `deleted`      smallint(4) NOT NULL COMMENT '是否有效（0=无效, 1=有效）', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), KEY `idx_taskId` (`task_no`, `config_type`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务配置表'; ``` |
| 4 | task_schedule：任务执行计划表 | task_schedule``` CREATE TABLE `task_schedule` (     `id`                     bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `operate_type`        smallint(4)  NOT NULL COMMENT '操作类型',     `schedule_status`        smallint(4) NOT NULL COMMENT '计划状态 10：待执行 20：执行中 30：执行成功 40：执行失败 50：已终止',     `scheduled_execute_time` datetime    NOT NULL COMMENT '计划执行时间',     `actual_start_time`      datetime COMMENT '实际执行时间',     `actual_end_time`        datetime COMMENT '执行结束时间', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), KEY `idx_taskId` (`task_no`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务执行计划表'; ``` |
| 5 | task_executor：任务执行人表 | task_executor``` CREATE TABLE `task_executor` (     `id`                       bigint(20) AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `executor_account_id`      varchar(64)  NOT NULL COMMENT '执行人-员工编码',     `executor_org_id`          varchar(64)  NOT NULL COMMENT '执行人-员工所属ORG ID',     `executor_account_type`    smallint(4) NOT NULL COMMENT '执行人-SSO类型:1:员工', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), UNIQUE KEY `uk_taskId_executor_emp_code` (`task_id`, `executor_emp_code`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '任务执行人表'; ``` |
| 6 | task_item_executor_relative：任务项执行人任务关联表 | task_item_executor_relative``` CREATE TABLE `task_item_executor_relative` (     `id`           bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `executor_id`  bigint(20)  NOT NULL COMMENT '执行人id',     `task_item_no` varchar(64)  NOT NULL COMMENT '任务项编号', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), UNIQUE KEY `uk_tId_exeId_tIid` (`task_no`, `executor_id`, `task_item_no`), KEY `idx_executorId` (`executor_id`), KEY `idx_taskItemId` (`task_item_id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务项执行人任务关联表'; ``` |
| 7 | task_operate_log：操作日志表 | task_operate_log``` CREATE TABLE `task_operate_log` (     `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',     `document_no`      varchar(64) NOT NULL COMMENT '事件唯一编号',     `document_type`    smallint(4) NOT NULL COMMENT '事件类型',     `current_operator` varchar(64) NOT NULL COMMENT '当前处理人工号',     `actual_operator`  varchar(64) NOT NULL COMMENT '实际操作人工号',     `operate_type`     smallint(4) NOT NULL COMMENT '操作类型',     `operate_desc`     varchar(64) NOT NULL COMMENT '操作描述',     `operate_time`     smallint(4) NOT NULL COMMENT '操作时间',     `content`          text COMMENT '操作内容', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务操作日志表'; ``` |
| 8 | task_notify_record：任务通知记录表 | task_notify_record``` CREATE TABLE `task_notify_record` (     `id`             bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `task_item_id`   bigint(20)  NOT NULL COMMENT '任务项id',     `notify_type`    smallint(4) NOT NULL COMMENT '通知类型 1：一心助手站内信 2：企业微信',     `notify_channel` varchar(64) NOT NULL DEFAULT '' COMMENT '通知渠道详情。',     `notify_target`  varchar(64) NOT NULL COMMENT '通知对象(员工工号或群ID)',     `notify_content` text COMMENT '通知内容',     `notify_status`  smallint(4) NOT NULL COMMENT '通知状态 1：发送成功 2：待重试 3：无需通', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务通知记录表'; ``` |
| 9 | task_item_result_relative 任务项结果信息表 | task_item_executor_relative``` CREATE TABLE `task_item_result_relative` (     `id`           bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键', `task_no` varchar(64) NOT NULL COMMENT '任务编号',     `task_item_no` varchar(64) NOT NULL COMMENT '任务项编号',     `biz_type`     bigint(20)  NOT NULL COMMENT '业务类型',     `biz_id`       varchar(64) NOT NULL COMMENT '业务编号', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`), UNIQUE KEY `uk_tId_exeId_tIid` (`task_no`, `task_item_no`, `biz_id`), KEY `idx_executorId` (`biz_id`), KEY `idx_taskItemId` (`task_item_id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务明细表'; ``` |
| 10 | task_template | sql``` CREATE TABLE `task_template` (     `id`                bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键',     `template_no`       varchar(64)  NOT NULL COMMENT '模板编号',     `template_name`     varchar(64)  NOT NULL COMMENT '模板名称',     `task_category`     smallint(4)  NOT NULL COMMENT '任务种类枚举 0:门店任务 1:个人任务',     `supported_channel` varchar(256) NOT NULL COMMENT '支持的创建方式 JSON',     `config`            text         NULL COMMENT '模板配置',     `enable_status`     smallint(4)  NOT NULL COMMENT '启用状态', `````` `deleted`        smallint(4) NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`        bigint(20)  NOT NULL COMMENT '版本号',     `created_time`   datetime    NOT NULL COMMENT '创建时间',     `created_name`   varchar(64) NOT NULL COMMENT '创建人员工工号',     `updated_time`   datetime    NOT NULL COMMENT '更新时间',     `updated_name`   varchar(64) NOT NULL COMMENT '更新人员工工号', `````` PRIMARY KEY (`id`),   UNIQUE KEY `uk_tmp_no` (`template_no`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='任务模板表'; ``` |
| 11 | 任务项执行人表本次没有使用-暂时移除-备份 | ``` create table if not exists task_item_executor ( id bigint auto_increment comment '主键'  primary key, task_no varchar(64) not null comment '任务编号', task_item_no varchar(64) not null comment '任务项编号', executor_account_id varchar(64) not null comment '执行人账号id', executor_account_type smallint not null comment '执行人-员工编码', executor_org_code varchar(64) null comment '执行人所属组织Code', version bigint not null comment '版本号', deleted smallint not null comment '数据是否有效 0无效 1有效', created_name varchar(64) not null comment '创建人工号', created_time datetime not null comment '创建时间', updated_name varchar(64) not null comment '更新人工号', updated_time datetime not null comment '更新时间' ) comment '任务项执行人表' charset = utf8mb4; ``` |
| 12 | 任务导入记录表 | sql``` CREATE TABLE `task_import_record` (     `id`                    bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键',     `record_no`             varchar(64)   NOT NULL COMMENT '导入记录编号',     `biz_type`              smallint(4)   NOT NULL COMMENT '业务类型，1=门店任务导入，2=个人任务导入',     `biz_no`                varchar(64)   NOT NULL COMMENT '业务编号',     `upload_file_info`      varchar(2048) NOT NULL COMMENT '导入文件信息 JSON',     `upload_fail_file_info` varchar(2048) NULL COMMENT '导入失败文件信息 JSON',     `import_status`         smallint(4)   NOT NULL COMMENT '导入状态状态， 10=成功，20=失败',     `deleted`               smallint(4)   NOT NULL COMMENT '数据是否有效(0:有效, 1:无效)',     `version`               bigint(20)    NOT NULL COMMENT '版本号',     `created_time`          datetime      NOT NULL COMMENT '创建时间',     `created_name`          varchar(64)   NOT NULL COMMENT '创建人员工工号',     `updated_time`          datetime      NOT NULL COMMENT '更新时间',     `updated_name`          varchar(64)   NOT NULL COMMENT '更新人员工工号',     PRIMARY KEY (`id`),     UNIQUE KEY `uk_record_no` (`record_no`),     KEY `idx_biz_no` (`biz_no`) ) ENGINE = InnoDB   DEFAULT CHARSET = utf8mb4 COMMENT ='任务导入记录表'; ``` |


## 5.3 接口设计

响应消息体结构：

单体响应消息：

jfx单体消息{
    "status":{
        "code":1,
        "message":"成功"
    },
    "data":{
        "id":123
    }
}  

分页响应消息：

{
    "status":{
        "code":1,
        "message":"成功"
    },
    "data":{
        "id":123
    },
    "page":{
        "pageNo":1,
        "pageSize":10,
        "pageTotle":50,
        "pageCount":5
    }
}

1.模板管理

- 模板列表查询接口
- 模板名称修改接口


2.任务管理

- 任务列表查询接口
- 创建任务接口
- 编辑任务接口
- 发布任务接口
- 撤回任务接口
- 停止任务接口
  - 任务项列表接口
  - 任务项执行详情接口
- 任务项列表接口
- 任务项执行详情接口


3. 用户端

- 待办列表查询接口
- 全部任务列表查询接口
- 任务详情接口


## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、 项目排期

**研发工时：11pd，自测工时：4pd，联调工时：pd；**

**研发时间：2023年月日-2023年月日（含研发自测）；联调时间：2023年11月日-2023年11月日；测试时间：2023年11月24日-2023年11月28日；上线时间：2023年月**日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD |  | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| **场景排查** **用户开始下单** | **【5.3.1.2】 线上身份核验（微信小程序）** | **前端组** |  | **0.5** | **姓名** **身份证** **位置经纬度** **临时授权码** **(信息生命周期按单走)** **每次提交订单携带给订单前台** |  |  |  |
| **【5.3.1.4】 唤起收银台** | **前端组** |  | **1** |  |  |  |  |
| **【5.3.1.5】 订单查询** | **前端组** |  | **0.5** |  |  |  |  |
| **【5.3.2.1】 待缴费费用明细查询** | **后端** **被动接口** |  | **0.5** | **订单小前台** **供东软收银台查询** **构建预支付商品信息** |  | 国枫 |  |
| **【5.3.2.2】 结算结果通知** | **后端** **被动接口** |  | **1** | **订单小前台**  **触发轮询任务** **使用【5.3.1.5】 订单查询结果 前台->中台->支付模块** |  | 国枫 |  |
| **【5.3.1.5】 订单查询** | **后端** **接口对接** |  | **0.5** | **订单前台调用** **订单中台提供** **对接正向单查询支付信息** |  | 国枫 |  |
| **定时撤销** | **后端** |  | **0.5** | **订单小前台** **针对超时未支付任务做定时check** **使用【5.3.1.6】 订单撤销订单** |  | 国枫 |  |
| **【5.3.1.6】 订单撤销** | **后端** **接口对接** |  | **0.5** | **订单小前台** |  | 国枫 |  |
| **【5.3.1.7】 订单退款** | **后端** **接口对接** |  | **0.5** | **支付中台** |  | 世达 |  |
| **保山医保退款申请增加授权码 从前台到中台传递** | **后端** |  | **1** | **支付中台** **前台→支付中台存储** **在实际调用【5.3.1.7】 订单退款需要拿出来使用** |  | 世达 |  |
| **订单退款后需调用订单查询进行状态确认** | **后端** |  | **0.5** | **支付中台** **订单中台提供** **【5.3.1.5】 订单查询** |  | 世达 |  |
| **自测** | **后端** |  | **2** |  |  | 世达 国枫 |  |
| ******运营平台** | **保山医保支付新增必填参数表单配置** | **前端组** |  | **1** | **** |  |  |  |
| **新增门店支付配置【保山医保】** | **后端** |  | **0.5** | **支付中台** |  | 世达 |  |
| **保山医保支付新增必填参数表单配置** | **后端** |  | **1** | **支付中台** |  | 世达 |  |
| **支付账单下载新增支持（保山医保）** | **后端** |  | **1** | **支付中台** **定时调用生成下载记录** |  | 世达 |  |
| **【5.3.1.8】 对账单下载** | **后端** **接口对接** |  | **0.5** | **支付中台** |  | 世达 |  |
| **微商城订单详情信息显示医保支付明细** | **后端** |  | **0.5** | **订单中台** |  | 国枫 |  |
| **订单导出数据增加医保支付信息明细** | **后端** |  | **0.5** | **订单中台** |  | 国枫 |  |
| **自测** | **后端** |  | **1.5** |  |  | 世达 国枫 |  |
| **一心到家小程序** | **1. 根据当前订单门店配置医保支付启用情况显示保山医保支付入口；** | **前端组** |  | **0.5** |  |  |  |  |
| **2. 调试微信在线医保电子凭证授权的交互；（首次授权需要进行用户身份信息保存）** | **前端组** |  | **1.5** |  |  |  |  |
| **3. 调试东软插件收银台的支付回调；** | **前端组** |  | **1** |  |  |  |  |
| **1. 医保支付订单信息中对应医保支付商品卡片显示医保支付标签；** | **前端组** |  | **0.5** |  |  |  |  |
| **2. 订单支付详情中显示医保支付、微信支付明细；** | **前端组** |  | **1** |  |  |  |  |
| **3. 含医保支付的订单退款时需增加微信在线医保电子凭证授权交互；** | **前端组** |  | **1** |  |  |  |  |
| **4. 含医保支付的订单退款时固定跳过选择退款商品页面，默认只能整单全退；** | **前端组** |  | **0.5** |  |  |  |  |
| **根据当前商品医保信息以及门店医保配置情况显示可医保支付商品标记** | **后端** |  | **1** | **商品中台** **找长江协调人力** |  |  |  |
| **2. 订单支付详情中显示医保支付、微信支付明细；** | **后端** |  | **1** | **订单前台** **支持查询支付明细含医保** **微信支付流水号记录** |  | 国华 |  |
| **自测** | **后端** |  | **0.5** |  |  | 国华 |  |
| **** | **** | **** |  | **** |  |  |  |  |
| **** | **** | **** |  | **** |  |  |  |  |
| **场景排查** **用户开始下单** | **线上身份核验（微信小程序），唤起收银台** | **测试组** |  | **1** |  |  | 鲁宇航 |  |
| **订单查询，待缴费费用明细查询，定时撤销** | **测试组** |  | **1** |  |  | 鲁宇航 |  |
| **订单撤销，订单退款** | **测试组** |  | **1.5** |  |  | 鲁宇航 |  |
| **订单退款后需调用订单查询进行状态确认** | **测试组** |  | **0.5** |  |  | 鲁宇航 |  |
| ******运营平台** | **保山医保支付新增必填参数表单配置验证** | **测试组** |  | **0.5** |  |  | 王越春 |  |
| **新增门店支付配置【保山医保】** | **测试组** |  | **0.5** |  |  | 王越春 |  |
| **保山医保支付新增必填参数表单配置** | **测试组** |  | **0.5** |  |  | 王越春 |  |
| **支付账单下载新增支持（保山医保）** | **测试组** |  | **0.5** |  |  | 王越春 |  |
| **微商城订单详情信息显示医保支付明细** | **测试组** |  | **0.5** |  |  | 王越春 |  |
| **订单导出数据增加医保支付信息明细** | **测试组** |  | **0.5** |  |  | 王越春 |  |
| **一心到家小程序** | **1. 根据当前订单门店配置医保支付启用情况显示保山医保支付入口** | **测试组** |  | **0.5** |  |  | 张柳 |  |
| **2. 调试微信在线医保电子凭证授权的交互；（首次授权需要进行用户身份信息保存）** | **测试组** |  | **1.5** |  |  | 张柳 |  |
| **1. 医保支付订单信息中对应医保支付商品卡片显示医保支付标签；** | **测试组** |  | **0.5** |  |  | 张柳 |  |
| **2. 订单支付详情中显示医保支付、微信支付明细；** | **测试组** |  | **1** |  |  | 张柳 |  |
| **3. 含医保支付的订单退款时需增加微信在线医保电子凭证授权交互；** | **测试组** |  | **1.5** |  |  | 张柳 |  |
| **4. 含医保支付的订单退款时固定跳过选择退款商品页面，默认只能整单全退；** | **测试组** |  | **1** |  |  | 张柳 |  |
| **根据当前商品医保信息以及门店医保配置情况显示可医保支付商品标记** | **测试组** |  | **1** |  |  | 张柳 |  |
| **2. 订单支付详情中显示医保支付、微信支付明细；** | **测试组** |  | **1** |  |  | 张柳 |  |


# 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等