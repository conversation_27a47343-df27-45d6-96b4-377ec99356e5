# 【20250219】网关升级SpringBoot版本

### Green跟进中

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-4556

### 背景

网关到下游服务的链路有问题。

### 其他问题

skywalking的版本已经升级了，springboot的版本还停留在低版本。日志有报错。需要解决

------

[https://yxtgit.hxyxt.com/java-global/hydee-spring-boot-starter-parent-pa](https://yxtgit.hxyxt.com/java-global/hydee-spring-boot-starter-parent-pa)

[https://yxtgit.hxyxt.com/java-global/yxt-grey-pa-spring-boot-starter](https://yxtgit.hxyxt.com/java-global/yxt-grey-pa-spring-boot-starter)

[https://yxtgit.hxyxt.com/basicservice/hydee-api-gateway](https://yxtgit.hxyxt.com/basicservice/hydee-api-gateway)

项目:

hydee-spring-boot-starter-parent-pa

hydee-api-gateway

yxt-grey-pa-spring-boot-starter.grey-spring-boot-gateway-starter

分支:

feature-upgrade-skywalking

版本: feature-upgrade-skywalking-SNAPSHOT

删除了POSCheck过滤器，检查开发、测试、生产配置，如果有要看下是否有表要留

如果配置有，修改POSCheck→NewPOSCheck。到时候再整理下配置

**Skywalking包**

D:\software\skywalkingPackage

spring:
  main:
    allow-circular-references: true

--------------------v2尝试----------------

项目:
hydee-spring-boot-starter-parent-pa
hydee-api-gateway



分支:

feature-upgrade-skywalking-v2

sdk前缀:

feature-upgrade-skywalking-v2-SNAPSHOT