# 美团闪购

# 1. 开放平台地址:https://open-shangou.meituan.com/

# 2. 接口系统级参数

API参数共由两部分组成, 系统级参数指所有由美团提供的API所必须的参数, 应用级参数是指对应API所需的参数。一般情况下, 系统级参数是调用API时所必须传递的参数

| API接口 | 接口描述 | 接口描述 |
| --- | --- | --- |
| timestamp | long | 调用接口时的时间戳，即当前时间戳（当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix - timestamp），注意传输时间戳与当前北京时间前后相差不能超过10分钟 |
| app_id | string | 美团分配给APP方的id |
| sig | string | 输入参数计算后的签名结果 |


# 3. 订单类API

## 3.1 获取订单详细信息(文档地址:https://tscc.meituan.com/home/<USER>/134)

接口说明：
1.用于商家主动查询订单当前的详细信息，仅支持查询近5天内的订单。如果所查订单超过近5天但未超过14天，接口返回“只能查5天内订单”；如果所查订单超过近14天，接口返回“不存在此订单”。
2.如需查询美团配送订单的配送相关信息，请上传is_mt_logistics字段，即is_mt_logistics=1。
3.查询接口返回信息中有较多字段默认不返回，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅相关字段。
4.商家调用此接口查询订单详情时，若出现获取的数据中商品信息为空的情况，建议商家自行对照商家端后台此订单商品，排查该门店内是否已删除这个商品。删除分两种情况：
(1)商家自行删除商品
订单商品被删除的15天内，合作中心查询订单详情接口仍然支持获取此已删除商品的信息；若删除超过15天，查询时信息则为空。
(2)平台审核删除商品
若商品被美团平台审核删除，例如商品图片中含有二维码而被删除。此类由平台按违规处理删除的商品，接口侧立即不支持查询，当商家调用接口查询订单详情时商品信息则为空。
注意，若订单还在进行中，也会影响订单推送消息中数据的完整性，导致商家解析失败。
建议商家遵循美团外卖平台的要求上传商品，如有不合规信息请及时调整。关于平台商品规则，请咨询品牌运营。
4.如订单中商品的app_spu_code和sku_id信息有变更，通过此接口查询订单时，这两个字段会展示下单时的app_spu_code与sku_id。
5.如订单商品的名称、规格、价格、单位信息有修改，通过此接口查询订单时，即此类信息不变化。
6.目前订单类接口中，sku_benefit_detail 字段里会展示享受优惠均摊的商品信息，请商家仔细参考[订单优惠均摊使用说明](https://opendj.meituan.com/home/<USER>/4613)
7.目前订单类接口中，sku_benefit_detail字段展示订单中商品sku维度的优惠金额分摊明细的规则为：
① 只展示享优惠分摊的活动商品均摊明细；未参与优惠分摊的商品相关信息不在此字段中展示。
② 当订单中同一商品sku参与同一种活动（活动id相同），多件数量时则商品优惠明细会合并展示；同时wmAppOrderActDetails中count字段表示此商品sku参与本活动的次数。

### 3.1.1 接口地址:https://waimaiopen.meituan.com/api/v1/order/getOrderDetail(GET请求)

### 3.1.2 入参

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| order_id | long | 是 | 27061900338318741 | 订单号，商家可根据订单号查询订单当前的详细信息。注:该字段支持传入商家端打印小票上面的订单号，平台内部会转成对应真实的订单号 |
| is_mt_logistics | int | 否 | 1 | 是否为美团配送，当需要查询美团配送的详细信息时，此字段需上传且取值为1。 |


### 3.1.3 出参

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| data | object |  |  |
| order_id | long | 27061900338318741 | 订单号（同订单展示ID），数据库中请用bigint(20)存储此字段。 |
| wm_order_id_view | long | 27061900338318741 | 订单展示ID |
| app_poi_code | string | 668921 | APP方门店id |
| wm_poi_name | string | 超市01店 | 商家门店名称 |
| wm_poi_address | string | 林芝墨脱县色金拉10号 | 商家门店地址 |
| wm_poi_phone | string | 01056****06 | 商家门店联系电话 |
| recipient_address | string | 色金拉 (色金拉)@#西藏自治区林芝市墨脱县色金拉 | 订单收货人地址 |
| delivery_position | strig | 门把手 | 骑手放置位置 |
| recipient_phone | string | 13812345678_1236 | 订单收货人联系电话 |
| backup_recipient_phone | list | ["13812345678_1236","13812345678_3456"] | 备用隐私号 |
| recipient_name | string | 测试（先生） | 订单收货人姓名 |
| shipping_fee | float | 0.01 | 门店配送费，单位是元。当前订单产生时该门店的配送费（商家自配送运费或美团配送运费），此字段数据为运费优惠前的原价。 |
| total | double | 4.31 | 订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。 |
| original_price | double | 6.31 | 订单的总原价，单位为元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。 |
| caution | string | 【如遇缺货】： 缺货时电话与我沟通 收货人隐私号 18689114387_3473，手机号 185****2033 | 订单备注信息 |
| shipper_phone | string | 185*****789 | 配送员联系电话 |
| status | int | 4 | 订单状态 1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消。 |
| city_id | long | 542600 | 城市id |
| has_invoiced | int | 1 | 是否支持开发票：0-不支持，1-支持。 |
| invoice_title | string | *******有限公司 | 发票抬头 |
| taxpayer_id | string | 91*************** | 纳税人识别号 |
| ctime | long | 1558955579 | 订单创建时间 |
| utime | long | 1558955891 | 订单更新时间 |
| delivery_time | long | 0 | 预计送达时间 |
| is_third_shipping | int | 0 | 是否是第三方配送平台配送，0-否（含商家自配送和美团配送，目前无法区分，具体配送方式建议参考logistics_code字段。）；1-是（第三方平台配送）。 |
| pay_type | int | 2 | 支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。 |
| pick_type | int | 0 | 取货类型：0-普通(配送),1-用户到店自取 |
| latitude | double | 29.774491 | 订单收货地址的纬度 |
| longitude | double | 95.369272 | 订单收货地址的经度 |
| day_seq | int | 3 | 当日订单流水号，门店每日已支付订单的流水号从1开始 |
| is_favorites | boolean | true | 订单用户是否收藏此门店：true-是， false-否。 |
| is_poi_first_order | boolean | false | 订单用户是否第一次在此门店下单：true-是，false-否 |
| is_pre_order | boolean | true | 是否为预订单，true-是，false-否 |
| is_pre_sale_order | boolean | true | 是否为预售单，true-是，false-否 |
| dinners_number | int | 0 | 用餐人数 |
| logistics_code | string | 0000 | 订单配送方式，该字段信息默认不返回。 |
| pickup_code | string | 1442 | 取件码 |
| poi_receive_detail | string |  | 订单维度的商家对账信息，json格式数据 |
| detail | string |  | 订单商品详情，其值为由list |
| extras | string |  | 订单优惠信息，其值为由list |
| sku_benefit_detail | string |  | 商品优惠详情 |
| user_member_info | string | {"card_code":"****************","is_poi_member":true,"level_code":"2","poi_member_id":"602685"} | 订单用户会员信息 |
| avg_send_time | double | 2360.0 | 门店平均送货时长，单位是秒 |
| order_send_time | long | 1558886665 | 用户下单完成支付的时间 |
| order_shipping_address | string | 北京市朝阳区东湖街道 | 收货人具体地址，精确到街道维度（省-市-区-街道） |
| order_receive_time | long | 1558909327 | 商家收到订单的时间，为10位秒级的时间戳。 |
| order_confirm_time | long | 1558909385 | 商家确认订单(接单)的时间，为10位秒级的时间戳。 |
| order_cancel_time | long | 143254562 | 订单取消时间，为10位秒级的时间戳。 |
| order_completed_time | long | 1558909972 | 订单完成时间，为10位秒级的时间戳。 |
| logistics_status | int | 40 | 美团配送订单状态code，目前美团配送状态值有：0-配送单发往配送，5-配送侧压单，10-配送单已确认，15-骑手已到店，20-骑手已取货，40-骑手已送达，100-配送单已取消。 |
| logistics_id | int | 1 | 配送方id，配送方id对应配送方名称：1-商家自配；2-趣活；3-达达；4-美团跑腿；5-加盟；6-自建；7-E代送；8-城市代理；100-角马；101-快送；102-混合送（专送+快送）。 |
| logistics_name | string | 商家自配 | 配送方名称，配送方id对应配送方名称：1-商家自配；2-趣活；3-达达；4-美团跑腿；5-加盟；6-自建；7-E代送；8-城市代理；100-角马；101-快送；102-混合送（专送+快送）。 |
| logistics_send_time | int | 1558908665 | 美团配送单发往配送的时间，此字段信息取最新一次发往配送的时间(logistics_status=0)，为10位秒级的时间戳。 |
| logistics_confirm_time | int | 1558908893 | 美团配送单确认时间，此字段信息取最新一次配送单确认的时间(logistics_status=10)，为10位秒级的时间戳。 |
| logistics_cancel_time | int | 0 | 美团配送单取消时间，此字段信息取最新一次配送单取消的时间(logistics_status=100)，为10位秒级的时间戳。如最新一次配送单最终未取消，则此字段值为0。 |
| logistics_fetch_time | int | 1558909385 | 美团骑手取单时间，此字段信息取最新一次骑手取单的时间(logistics_status=20)，为10位秒级的时间戳。 |
| logistics_completed_time | int | 1558909972 | 美团配送单完成时间，即骑手送达订单商品的时间(logistics_status=40)，为10位秒级的时间戳。 |
| logistics_dispatcher_name | string | 美小配 | 美团配送骑手的姓名，取最新一次指派的骑手信息。 |
| logistics_dispatcher_mobile | string | 18329667286_1234 | 美团配送骑手的联系电话，取最新一次指派的骑手信息。 |
| package_bag_money | int | 30 | 订单维度的打包袋金额，单位是分 |
| estimate_arrival_time | int | 1558910221 | 订单预计送达时间，为10位秒级的时间戳 |
| package_bag_money_yuan | string | 2.00 | 订单维度的打包袋金额，单位是元 |
| poi_receive_detail_yuan | string |  | 订单维度的商家对账信息，json格式数据。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“38 商家应收款详情(元)”。 与poi_receive_detail字段相比，poi_receive_detail_yuan字段里的金额类字段单位为元，其他规则相同；订阅其中一个字段即可。 |
| total_weight | long | 22 | 订单商品的总重量（该信息默认不返回，可在开发者中心订阅），单位为克/g。 |
| order_phone_number | string | 13100000000_1234 | 推送订单的预订人手机号，适用于鲜花绿植类和美妆日化类商家的订单。请兼容支持真实号码和隐私号两种格式，字段推送规则： 1.当下单用户关闭隐私号保护，则字段推送预订人真实手机号码，示例：13000220022。 2.当下单用户开启隐私号保护，则字段推送预订人手机号码隐私号格式，示例：13100000000_1234。 字段默认内容： 当用户选择隐私保护：【预订人】1xxxxxxxxxx_xxxx 当用户未选择隐私保护：【预订人】1xxxxxxxxxx |
| incmp_code | int | 0 | 订单数据状态标记。当订单中部分字段的数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值： -1：有数据降级 0：无数据降级 |
| incmp_modules | set |  | 有降级的数据模块的集合，参考值： 0：订单商品详情 1：订单优惠信息 2：商品优惠详情 3：订单用户会员信息 4：订单维度的商家对账信息 5：订单维度的商家对账信息(元) 6：订单收货人地址 7：订单配送方式 8：开放平台用户id 9：部分退款商品信息 10：退货退款物流信息 11：部分订单基本信息(包括订单优惠信息、订单商品详情、门店信息等) 12：sku信息 13：spu信息 14：商品信息(可能是sku或spu等商品相关信息获取时降级) 15：替换折扣价为原价 |
| scan_deliver_flag | boolean | true | 该订单是否扫码配送，参考值：ture-是，false-否 |
| scan_deliver_qr_content | string | {"business":"peisong-qrfetch","content":"1624614860138003398"} | 扫码配送的二维码内容，仅当订单为扫码配送（scan_deliver_flag =true）时，本参数才返回。需开发者自行转换成打印机指令发送给打印机系统，将二维码打印在订单小票上。 |
| is_whole_city_ship | int | 0 | 是否是全城送订单：1-是，0-否 |
| order_tag_list | set<Integer> | [8,22] | 订单标识：21、22-周期购订单 |
| cycle_info | object | {"delivery_infos":[{"cycle_detail":[{"app_spu_code":"66633","name":"毛巾毛巾","quantity":4,"sku_id":"10"}],"estimate_arrival_date":20220524,"estimate_arrival_time_end":1653379200,"estimate_arrival_time_start":1653375600,"index":1,"logistics_status":40}],"rule":{"day":"1,2,3,4,5","type":2},"time":"15:00-16:00","total_count":5} | 周期购订单详情 |


## 3.2 根据流水号获取订单ID（分段）(文档地址:https://tscc.meituan.com/home/<USER>/341)2024年3月30日停用此接口!!!

**接口限流：5次/秒**

接口说明：

1.此接口用于商家根据日期、流水号批量查询订单号，仅支持查询近30天内日期的订单。

2.调用此接口查询订单号，支持部分成功。

### 3.2.1 接口地址:https://waimaiopen.meituan.com/api/v1/ecommerce/order/getOrderIdByDaySeq(GET请求)

### 3.2.2 入参

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| app_poi_code | string | 是 | 668921 | APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。 |
| date_time | int | 是 | 20190212 | 需查询的订单产生日期，整型数据。仅支持查询近30天内的订单。 |
| day_seq_start | int | 是 | 1 | 订单流水号的开始序号，门店内每日的订单流水号都是从1开始。 |
| day_seq_end | int | 是 | 100 | 订单流水号的结束序号，注意开始流水号与结束流水号的跨度需小于100，即差值最大为99。 |


### 3.2.3 出参

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| data | list<object> |  | 如全部失败，此字段返回ng。 |
| result |  | ok | 如全部成功或部分成功，此字段返回ok。 |
| order_ids | string | [27061900237027975,27061901214977976,27061900887535258] | 此字段信息返回按流水号升序顺序的订单号list，多个以英文逗号隔开。 |
| error |  |  |  |
| msg | string | 4:不存在此订单|5:不存在此订单|6:不存在此订单|7:不存在此订单 | 此字段信息返回无对应订单号的流水号 |
| code | int | 0 | 错误码code：(1)如全部成功或部分成功，此字段code信息为0。(2)如全部失败，此此字段code信息为808。 |


## 3.3 批量查询门店订单号 (文档地址:https://tscc.meituan.com/home/<USER>/10125)

 本接口使用游标查询，第一次无需传入游标，假设第一次查询设置page_size=20，门店当日有50个订单，会查询到20个订单，

游标返回211132，下次翻页查询需传入vernier=211132，如需查询当日所有订单，则一直向后翻页直到接口返回数据为空。

### 3.3.1 接口地址:https://waimaiopen.meituan.com/api/v1/ecommerce/order/getOrderIdByPage(POST请求)

### 3.3.2 入参

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| app_poi_code | String | 是 | 6113452 | 三方门店code |
| start_time | int | 否 | 1700129837 | 开始时间，unix时间戳（秒级），不传默认取当日零点 |
| end_time | int | 否 | 1700129899 | 结束时间，unix时间戳（秒级），不传默认取当前时间 开始和结束时间相差不能超过七天，只能查最近90天内的订单 |
| vernier | String | 否 | vbiugfuodbqiuh2h89hd19hw89hdf47382gf6yedu== | 游标，如果想要查询当日所有订单，第一次查询时无需传入游标，如需翻页，后续每次查询都需要将上一次接口返回的游标代入 |
| page_size | int | 否 | 20 | 游标查询单次查询订单数量范围，默认20，最大支持100 |


### 3.3.3 出参

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| result_code | int | 1 | 1-全部成功 3-全部失败 4-限流、验签错误 |
| success_map | object | { "vernier": "qRp4d73FIjiDdnFE8AgVHEcfyJZCc34Si30pyxibJj8=", "has_more": 1 } |  |
| vernier | int | qRp4d73FIjiDdnFE8AgVHEcfyJZCc34Si30pyxibJj8= | 游标值，如果需要翻页查询，下一次需要传入此游标 |
| hasMore | int | 1 | 是否有下一页数据 |
| success_list | list<object> | [ { "order_id": 3200862622618142200 }, { "order_id": 3200862621745437879 }, { "order_id": 3200862660868854464 }, { "order_id": 3200862663000503152 }, { "order_id": 3200862694065561437 } ] | 此字段信息返回订单下单顺序升序（不保证强有序，总体有序）的订单号列表 |
| order_id | long | 3200862694065561437 | 订单展示ID |
| error_list | list<object> |  |  |
| msg | string | 门店不存在 | 错误信息 |
| code | int | 808 | 错误码code |


## 3.4 查询订单的处方信息

接口说明：

1.根据订单id查询已完成订单对应的处方信息（仅支持t-1以及之前数据）

### 3.3.1 接口地址:https://waimaiopen.meituan.com/api/v1/gw/order/hospital/rp/used/list(GET请求)

### 3.3.2 入参

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| order_id | long | 是 | 27061900338318741 | 订单号（同订单展示ID），商家可根据订单号查询订单当前的处方信息。 |


### 3.3.3 出参

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| code | int | 0 | 0:成功 |
| msg | string |  | 描述信息 |
| data | list<object> |  | 处方信息 |
| rpId | long |  | 处方id |
| medicalUserName | string |  | 用药人姓名 |
| gender | int |  | 用药人性别，1, "男"；2, "女" |
| birthday | string | 2017-11-07 | 用药人出生日期 |
| hospitalName | string |  | 开方医院名称 |
| doctorName | string |  | 医生姓名 |
| createTime | string | 2022-08-08 11:32:27 | 处方开具时间 |
| icdName | string | 心脏病,高血压 | 诊断 |
| rpPicUrl | string |  | 处方图片 |


# 4. 门店类API

## 4.1 已授权商家账号所关联门店的绑定与解绑

接口限流：20次/秒
接口说明：
1. 本接口支持对已授权至应用的商家账号所关联的非下线门店进行绑定、解绑、修改三方门店ID。已下线门店不支持绑定和修改三方门店ID；支持解绑已下线门店。
2. 本功能接口仅适用于连锁商家申请权限，暂不支持软件服务商的ISV应用使用。操作流程请参考[《商家连锁账号授权接口操作指南》](https://open-shangou.meituan.com/home/<USER>/12244)。
3. 调用本接口操作授权绑定门店或解绑门店，如有需要获知相关回调消息，可以对接回调接口[【APP方URL 推送门店绑定和解绑消息】](https://open-shangou.meituan.com/home/<USER>/438)。

### 4.1.1 接口地址:https://waimaiopen.meituan.com/api/v1/ecommerce/poi/bind/app/by/account(POST请求)

### 4.1.2入参

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| type | int | 是 | 1 | 操作类型。仅支持传枚举值： 1-授权绑定；2-解除关联；3-修改三方门店ID。 |
| wm_poi_id | long | 是 | 123456 | 美团门店ID，该门店的商家账号必须已绑定当前应用的前提下，才能通过此接口完成门店相关操作。 若上传的门店是下线状态，不支持绑定应用，也不支持修改三方门店ID。 1.type=1（绑定）时，上传门店（非下线）如已绑定，报错。 2.type=2（解绑）时，上传门店（非下线）如未绑定，报错。 3.type=3（修改三方ID）时，上传门店（非下线）如未绑定，报错。 4.type=2（解绑）时，上传的wm_poi_id与app_poi_code之间如没有关联关系，则报错。 |
| app_poi_code | String | 是 | abc | 三方门店ID，与当前应用内已绑定门店的三方ID不能重复。 |


### 4.1.3 出参

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| data | string | ng |  |
| error | string | { "code": 845, "msg": "门店不存在或门店所关联的商家账号当前没有授权绑定此应用" } |  |
| code | int | 845 | 错误code |
| msg | string | 门店不存在或门店所关联的商家账号当前没有授权绑定此应用 | 错误信息 |


## 4.2 批量获取门店详细信息

接口限流：50次/秒
接口说明：本接口用于商家批量获取已绑定至开放平台应用中的门店详细信息。

### 4.2.1 接口地址:https://waimaiopen.meituan.com/api/v1/poi/mget(GET请求)

### 4.2.2 入参

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| app_poi_codes | string | 是 | 666 | APP方门店id，传商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。 支持传多个门店id批量查询，一次调用可上传200个门店id，多个之间以英文逗号分隔；支持部分门店查询成功。 仅支持返回门店id正确的门店信息。 |


### 4.2.3 出参

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| data | list<object> |  | 门店信息，json格式数组。 |
| app_poi_code | string | 666 | APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。 |
| name | string | 测试店 | 门店名称 |
| address | string | 墨脱县色金拉10号 | 门店地址 |
| latitude | double | 2.9774566E7 | 门店纬度，美团使用的是高德坐标系。 注：如需使用获取的纬度信息在poi/save接口中上传，需用此返回信息再除以一百万。 例如本接口查询到的纬度值为2.9774566E7，那么在poi/save接口上传时，需转为29.774566（上传时平台系统会自动乘以一百万）。 |
| longitude | double | 9.5369278E7 | 门店经度，美团使用的是高德坐标系。 注：如需使用获取的经度信息在poi/save接口中上传，需用此返回信息再除以一百万。 例如本接口查询到的经度值为9.5369278E7，那么在poi/save接口上传时，需转为95.369278（上传时平台系统会自动乘以一百万）。 |
| pic_url | string | [http://p1.meituan.net/crm/__37375183__1582979.jpg](http://p1.meituan.net/crm/__37375183__1582979.jpg) | 门店图片地址 |
| pic_url_large | string | [http://p1.meituan.net/crm/__37375183__1582979.jpg](http://p1.meituan.net/crm/__37375183__1582979.jpg) | 门店图片地址 |
| phone | string | 010-10101010 | 门店客服电话号码 |
| standby_tel | string | 133232222 | 门店电话号码(已废弃) |
| shipping_fee | float | 1.0 | 每个订单的配送费，单位是元。 |
| shipping_time | string | 00:00-01:00,01:05-23:59;;;;00:00-01:00,01:05-23:59;; | 门店营业时间：同一天的多个时间段之间以英文逗号分隔；周一至周日每天之间用英文分号分隔，如不营业则无时间段，例如“00:00-01:00,01:05-23:59;;;;00:00-01:00,01:05-23:59;;”表示只有周一和周五营业，每天营业时间为00:00-01:00,01:05-23:59 |
| promotion_info | string | 这是测试门店，不会真实配送商品，用户请勿下单。 | 门店公告信息 |
| invoice_support | int | 1 | 门店是否支持发票，参考值：1-支持；0-不支持。 |
| invoice_min_price | int | 1 | 门店支持开发票的最小订单价，单位是元。 |
| invoice_description | string | 发票特殊说明：请核对开票抬头，错误概不负责。如只有定额发票，不支持开抬头等。 | 发票相关说明 |
| open_level | int | 1 | 门店的营业状态，参考值：1-可配送；3-休息中。 |
| is_online | int | 1 | 门店上下线状态，参考值：0-下线；1-上线；2-上单中；3-审核通过可上线。 |
| ctime | int | 1497607035 | 门店创建时间，返回10位秒级时间戳。（当前距离Epoch（1970年1月1日） 以秒计算的时间，即unix-timestamp） |
| utime | int | 1563789215 | 门店最近一次更新时间，返回10位秒级的时间戳。（当前距离Epoch（1970年1月1日） 以秒计算的时间，即unix-timestamp） |
| third_tag_name | string | 超市 | 门店经营品类，多个品类信息以英文逗号分隔。 |
| pre_book | int | 0 | 是否支持营业时间范围外预下单，参考值：1-支持；0-不支持。 |
| time_select | int | 1 | 是否支持营业时间范围内预下单，参考值：1-支持；0-不支持。 |
| logistics_codes | string | 0000,1003 | 门店的配送方式,参考值： 1003-美团跑腿（众包） 1001-专送（加盟） 1002-专送（自建） 1004-城市代理 2002-快送 2010-全城送 0000-商家自配 3001-混合送（专送+快送) 30011002-混合自建 30011001-混合加盟 30012002-混合快送 0002-趣生活美食配送 0016-达达快递 0033-E_代送 |
| pre_book_min_days | int | 0 | 商家接受预订日期的最早日期，范围：0-7。最早日期是指用户最少需要提前下单的天数，如果不选“当天”（传0），则用户不能下要求今日送达的订单。例如：最早日期为“隔天”（传1），则用户今日最快只能下明天备货配送的订单。 |
| pre_book_max_days | int | 1 | 商家接受预订日期的最长日期，取值范围：0-7。最长日期是指用户可要求送达的最多天数。例如：最长日期为“隔天”（传1），则用户今日可下要求明天配送的订单，不可下要求后天配送的订单。 |