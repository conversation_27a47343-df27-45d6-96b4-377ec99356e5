# 新零售一期上线切流复盘

一、上线切流SOP：

二、切换过程问题整理

[https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgouceSr1eTKuv7glhbJ?scode=AOsAFQcYAAcDjhk201AboAOAYLADg&version=4.1.12.6015&platform=win&tab=gcw4tt](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgouceSr1eTKuv7glhbJ?scode=AOsAFQcYAAcDjhk201AboAOAYLADg&version=4.1.12.6015&platform=win&tab=gcw4tt)

三、上线切换复盘

| 序号 | 模块 | 问题 | 原因 | 优化建议 | 反馈人 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单下账 | POS无法自动下账 | 门店POS系统未升级最新版本 | POS版本需要提前升级 |  |
| 2 | 订单入库 | 门店发现漏单. /报警机器人 环境：pro 服务：hydee-business-order 实例：************* TraceId：18a10ada31fe4a628380be0903156423.252.17016995792076319 [2023-12-04 22:19:39.207]-租户用户:[:]-[cn.hydee.middle.business.order.service.rabbit.OrderMessageConsumer49]-ERROR [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#7-3] OrderMessageConsumer process queue[OmsOrder], messageId:cc3930be24bb483e822f37a8a5ca587d, thirdOrderNo:2329190136000094 java.lang.NullPointerException: null  at cn.hydee.middle.business.order.v2.handlers.SaveOrderHandler.buildPublicMultiPay(SaveOrderHandler.java:107) | 订单入库时需要检索门店对应的下账配置;如果找不到会重试3次 | 1.产品需要提前整理切换平台+门店列表的下账配置. 并确认执行 |  |
| 3 | 订单入库 | 门店发现漏单. /环境：pro 服务：hydee-business-order 实例：************ TraceId：c9467caeff724177aebd7554c6812afd.122.17015648287294895 [2023-12-03 08:53:48.736]-租户用户:[:]-[cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos.KcPosGatewayImpl239]-ERROR [http-nio-12600-exec-10] KcPos getOrderList fail req:{"token":"71402817EFEFCF9090C114B17CC23A22","sign":"p|fs02VuvT5mBSnR9ocD4A--","erpstate":"1"} 未找到对应的门店信息 | 平台店铺编码为123213;心云配置为A002;匹配门店失败; | 1.产品需要提前整理切换平台+门店列表的店铺配置 并确认执行 |  |
| 4 | 订单下账 | 店铺自动下账流量一直报警500 | 门店POS提供的appkey与心云系统不一致; | 1.老系统创建新店铺是自动生成appkey,昆明开发李清提供给POS技术. 2.心云缺少与老系统新创店铺的联动3.人工补救方案.每次有新店铺,都需要刚哥修改数据库 |  |
| 5 | 订单下账 | 产品配置下账配置错误,导致下账金额生成错误. |  | 1.产品需要提前确认各个平台订单的下账配置并验证通过 2.已开发运维接口,支持重新计算下账金额.(支持未下账的) 3.已下账的无法补救 |  |
| 6 | 订单下账 | 1.门店未按照标准流程作业,骑手先拿货,后拣货. 2.过程中订单拉取到心云是已完成订单 | 缺少拣货信息无法下账. | 1.产品前置场景模拟不到位,自身对于业务场景也有了解不足的缺陷,考虑方案存在漏洞 2.缺少运维的入口,目前都是通过sql更改订单状态重新拣货.流程复杂且浪费人力 |  |
| 7 | 订单完结 | 1.已切店的老系统未完成订单,O2O场景,需要迁移订单数据到心云继续执行. |  | 1.产品前期定门店都为已闭店且无未完成订单,缺少24小时门店的场景考虑. 2.已新增代码同步机制 |  |
| 8 | 订单完结 | 2.未切店的老系统订单,下账状态需要拉取到心云同步. |  | 1.产品前期考虑的下账状态同步依赖POS系统全面上线.但是实际实施是按照分公司执行.所以未切店的下账流量监控不到;缺少与POS的沟通  2.已新增老系统订单的下账状态同步机制; |  |
| 9 | 支付回调消息丢失 | prod在发布中,接收到平台的回调消息,没有第一时间保存报文,执行过程长,引发丢失数据. |  | 1.运维增加prod健康监测时长 2.建议运维做到优雅发布/等待线程执行完结. 3.开发对于回调的架构做到幂等/健壮 例如接到第一时间保存报文.增加异步补偿机制.主动与平台做状态确认补偿. |  |
| 10 | 订单下账 | 在下账时发现,因美团 饿了么 京东到家 微商城 各个平台的独立性,对不同场景的金额字段解析也有所不同.例如运费优惠,打包费等 |  | 1.测试环境缺少更多样本数据,应提早在线上验证 2.线上验证也应该增加测试商家,做到配置,数据的隔离. |  |
| 11 | 订单配送 | 因平台差异,配送信息调用.net存在接口不存在报错/配送范围超出/预约单超出预约时间等 |  | 1.在测试环境无法验证到上述真实业务场景. |  |
| 12 | 打印助手 | 打印助手适配不同显示器,打印机,电脑 |  | 1.在测试环境缺少小票打印机等完整的门店操作环境区验证. 2.样本不足,前期没有覆盖到门店存在win7,win10,win11不同环境,打印机机型也不同,电脑显示器尺寸也不同. |  |