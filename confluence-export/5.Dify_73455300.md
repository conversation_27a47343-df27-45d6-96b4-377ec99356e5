# 5.Dify

## 产研Dify

体验Dify平台：
地址：[http://10.100.0.63/](http://10.100.0.63/)
账号：dify@[yxt.com](http://yxt.com)
密码：Dify@123!

产研共识: AI相关开发,优先使用Dify

## Dify使用文档

### 工作流

工作流通过将复杂的任务分解成较小的步骤（节点）降低系统复杂度，减少了对提示词技术和模型推理能力的依赖，提高了 LLM 应用面向复杂任务的性能，提升了系统的可解释性、稳定性和容错性。

Dify 工作流分为两种类型：

- **Chatflow**：面向对话类情景，包括客户服务、语义搜索、以及其他需要在构建响应时进行多步逻辑的对话式应用程序。
- **Workflow**：面向自动化和批处理情景，适合高质量翻译、数据分析、内容生成、电子邮件自动化等应用程序。


不同的工作流有不同的节点

### 节点

[https://docs.dify.ai/zh-hans/guides/workflow/node/start](https://docs.dify.ai/zh-hans/guides/workflow/node/start)

| 节点 | 是否必备 | 主要功能 |
| --- | --- | --- |
| trueBlue开始 | 2 complete | 提供必要的初始信息 |
| trueBlueLLM |  | 调用大语言模型的能力场景:- **意图识别**，在客服对话情景中，对用户问题进行意图识别和分类，导向下游不同的流程。 - **文本生成**，在文章生成情景中，作为内容生成的节点，根据主题、关键词生成符合的文本内容。 - **内容分类**，在邮件批处理情景中，对邮件的类型进行自动化分类，如咨询/投诉/垃圾邮件。 - **文本转换**，在文本翻译情景中，将用户提供的文本内容翻译成指定语言。 - **代码生成**，在辅助编程情景中，根据用户的要求生成指定的业务代码，编写测试用例。 - **RAG**，在知识库问答情景中，将检索到的相关知识和用户问题重新组织回复问题。 - **图片理解**，使用 vision 能力的多模态模型，能对图像内的信息进行理解和问答。 |
| trueBlue知识检索 |  | 从知识库中检索与用户问题相关的文本内容，可作为下游 LLM 节点的上下文来使用。 |
| trueBlue问题分类 |  | 通过定义分类描述，问题分类器能够根据用户输入，使用 LLM 推理与之相匹配的分类并输出分类结果，向下游节点提供更加精确的信息。Green重要 |
| trueBlue条件分支 |  | 拆分成多个分支。拆解复杂问题 Green重要 |
| trueBlue代码执行 |  | 在工作流程中执行数据转换。如果使用工作流比较繁琐，这个时候就要考虑是否使用代码来包一层了，快速解决。场景: 结构化数据处理\数学计算\拼接数据等 |
| trueBlue模板转换 |  | 允许借助 Jinja2 的 Python 模板语言灵活地进行数据转换、文本处理等。理解不常用如果后面有复杂的转换，会使用程序或者让另一个agent协作 |
| trueBlue文档提取器 |  | 主要解决LLM 自身无法直接读取或解释文档内容的缺陷。 |
| trueBlue列表操作 |  | 主要解决: 大语言模型（LLM）仅支持读取图片文件或文本内容等**单一值**作为输入变量，因为有的模型擅长图片，有的模型擅长文本(我理解这是现阶段模型能力不足的妥协方案)。 列表操作节点可以对文件的格式类型、文件名、大小等属性进行过滤与提取，将不同格式的文件传递给对应的处理节点，以实现对不同文件处理流的精确控制。 |
| trueBlue变量聚合 |  | 将不同分支下相同作用的变量映射为一个输出变量，避免下游节点重复定义。note:没啥，就是一个映射 |
| trueBlue变量赋值 |  | 通过变量赋值节点，你可以将工作流内的变量赋值到会话变量中用于临时存储，并可以在后续对话中持续引用。Green重要 |
| trueBlue迭代 |  | 对数组中的元素依次执行相同的操作步骤，直至输出所有结果，可以理解为任务批处理器。迭代节点通常配合数组变量使用。场景: 1. 长文本翻译，避免达到 LLM 单次对话的消息限制，将长文拆分为了多个片段，配合迭代节点对各个片段执行批量翻译。 2. 给一批订单号做xx操作 3. 给一批店铺做xx操作  使用trueBlue参数提取节点，可以配合代码，转为数据，然后吐给迭代器 |
| trueBlue参数提取 |  | 利用 LLM 从自然语言推理并提取结构化参数，用于后置的工具调用或 HTTP 请求。Green重要 |
| trueBlueHTTP 请求 |  | 处理常用的支持，还支持文件。高级功能: 重试、异常处理 |
| trueBlueAgent |  | Agent 节点是 Dify Chatflow/Workflow 中用于实现自主工具调用的组件。它通过集成不同的 Agent 推理策略，使大语言模型能够在运行时动态选择并执行工具，从而实现多步推理。Green重要Dify 内置了 **Function Calling 和 ReAct** 两种策略 |
| trueBlue工具 |  | “工具”节点可以为工作流提供强大的第三方能力支持Green重要- **内置工具**，Dify 第一方提供的工具，使用该工具前可能需要先给工具进行 **授权**。 - **自定义工具**，通过 [OpenAPI/Swagger 标准格式](https://swagger.io/specification/)导入或配置的工具。如果内置工具无法满足使用需求，你可以在 **Dify 菜单导航 —工具** 内创建自定义工具。 - **工作流**，你可以**编排一个更复杂的工作流，并将其发布为工具**。例如,订单算价工具。Green重要 |
| trueBlue结束 |  | 定义一个工作流程结束的最终输出内容。若流程中出现条件分叉，则需要定义多个结束节点。Chatflow 内不支持结束节点。 |
| trueBlue直接回复 |  | 定义一个 Chatflow 流程中的回复内容。 直接回复节点可以不作为最终的输出节点，作为流程过程节点时，可以在中间步骤流式输出结果。 |
| trueBlue循环 |  | 循环（Loop）节点用于**执行依赖前一轮结果的重复任务**，直到满足退出条件或达到最大循环次数。 |


### 文件上传

有的LLM不支持直接上传文件，需要添加文档提取器

### 异常处理

- 组件自带
- 或者预编排异常处理分支


### 结构化输出

[https://docs.dify.ai/zh-hans/guides/workflow/structured-outputs](https://docs.dify.ai/zh-hans/guides/workflow/structured-outputs)

### 插件

[https://docs.dify.ai/zh-hans/plugins/introduction](https://docs.dify.ai/zh-hans/plugins/introduction)

### 拓展

[https://docs.dify.ai/zh-hans/guides/tools/extensions/README](https://docs.dify.ai/zh-hans/guides/tools/extensions/README)

- API拓展 —— 就是请求接口
- 代码拓展


### 通过API访问

[访问API](https://docs.dify.ai/api-reference/%E5%AF%B9%E8%AF%9D%E6%B6%88%E6%81%AF/%E5%8F%91%E9%80%81%E5%AF%B9%E8%AF%9D%E6%B6%88%E6%81%AF)