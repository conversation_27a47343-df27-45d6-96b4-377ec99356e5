# 4.Spring Al Alibaba JManus源码阅读

源码阅读，最新commit id: 113bd178

开源项目会持续更新,请关注最新提交源码地址: [https://github.com/alibaba/spring-ai-alibaba/blob/main/spring-ai-alibaba-jmanus/README-zh.md](https://github.com/alibaba/spring-ai-alibaba/blob/main/spring-ai-alibaba-jmanus/README-zh.md)

### Spring AI Alibaba Java Manus 提供以下关键功能

1. 完美实现 OpenManus 多 Agent 框架：使用 Spring AI 和 Java 全面实现 OpenManus 架构。
2. 通过网页界面配置 Agent：通过直观的网页管理界面轻松配置 agent，无需修改代码。
3. MCP (Model Context Protocol) 接入 Agent：无缝集成 Model Context Protocol，使 agent 能够与各种模型和服务交互。
4. 支持 PLAN-ACT 模式：实现强大的 PLAN-ACT 模式，支持复杂的推理和执行工作流。


| 入口 | 思考-执行-总结 | 配置 |
| --- | --- | --- |
|  |  |  |


### 状态机



![jmanus-status](./resources/jmanus-status.png)



### 计划

计划流程的总协调器 负责协调计划的创建、执行和总结三个主要步骤



![jmanus-plan](./resources/jmanus-plan.png)



在项目初始化的时候,初始化了3个**ChatClient**，分别为计划、执行、总结

javatrue	public LlmService(ChatModel chatModel) {

		this.chatModel = chatModel;
		// 执行和总结规划，用相同的memory
		this.planningChatClient = ChatClient.builder(chatModel)
			.defaultAdvisors(new SimpleLoggerAdvisor())
			.defaultOptions(OpenAiChatOptions.builder().temperature(0.1).build())
			.build();

		// // 每个agent执行过程中，用独立的memroy

		this.agentExecutionClient = ChatClient.builder(chatModel)
			// .defaultAdvisors(MessageChatMemoryAdvisor.builder(agentMemory).build())
			.defaultAdvisors(new SimpleLoggerAdvisor())
			.defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
			.build();

		this.finalizeChatClient = ChatClient.builder(chatModel)
			.defaultAdvisors(MessageChatMemoryAdvisor.builder(conversationMemory).build())
			.defaultAdvisors(new SimpleLoggerAdvisor())
			.build();

	}

### 执行上下文



![jmanus-exe-context](./resources/jmanus-exe-context.png)



### Jmanus目录结构

├── OpenManusSpringBootApplication.java 启动类
├── agent  智能体顶层抽象
├── config 配置
│   └── startUp
│       ├── AppStartupListener.java
│       ├── ConfigAppStartupListener.java  启动的时候,进行一些初始化配置
│       └── QueryCommandRunner.java
├── dynamic
│   ├── agent 智能体具体实现
│   └── mcp mcp
├── llm 大模型
├── planning 计划
├── recorder 记录
└── tool 工具

Jmanus智能体实现继承树

| 继承树 |  |  |
| --- | --- | --- |
|  | BaseAgent | BaseAgent.java 是一个抽象基类，用于实现可执行多步骤任务的AI智能体。  它提供了智能体状态管理（如未开始、进行中、已完成）、对话流程跟踪、步骤限制与监控、线程安全执行以及卡住状态检测与处理等核心功能  子类必须实现名称、描述、思考逻辑、下一步提示模板和步骤执行等抽象方法，以构建具体的智能体实现。 |
| ReActAgent | ReAct（Reasoning + Acting）模式的智能体基类 实现了思考(Reasoning)和行动(Acting)交替执行的智能体模式。  该类定义了思考(think)和行动(act)两个抽象方法，要求子类实现具体的推理逻辑和行动执行。它提供了step()方法来协调思考-行动的完整流程，先通过think()判断是否需要行动，若需要则调用act()执行具体操作并返回结果，否则返回思考完成的状态。  **该类为不同类型智能体（如ToolCallAgent、BrowserAgent）提供了统一的思考-行动框架，通过抽象方法设计实现了行为的灵活扩展。** |
| DynamicAgent | ReAct（Reasoning + Acting）模式的实际实现,**连接了工具,与现实世界产生了交互** |


### 程序初始化多个Agent的过程



![dynamicAgent](./resources/dynamicAgent.png)



### 当发出一个指令时,jmaus做了什么

#### 核心流程



![jmanus running](./resources/jmanus_running.png)



#### 创建计划




![创建计划](./resources/创建计划.png)



#### 执行计划



![未命名绘图](./resources/未命名绘图_6.png)



##### 执行计划-think()流程



![think()](./resources/think().png)



##### 执行计划-act()流程



![act()](./resources/act().png)



#### 生成总结



![总结](./resources/总结.png)



#### JManus如果需要人工确认,是怎么交互的?

在需要交互的地方，休眠，等待用户输入,等待超过指定时间则超时(现在超时会将代理置为IN_PROCESS继续执行)。

用户输入会放在[com.alibaba.cloud.ai](http://com.alibaba.cloud.ai).example.manus.tool.FormInputTool.UserFormInput#inputs属性中，并标记InputState.INPUT_RECEIVED。

上面休眠结束后，检查如果InputState是INPUT_RECEIVED，则从inputs属性中读取，放在大模型的记忆中

### 我对Agent的理解



![对Agent的理解](./resources/对Agent的理解.png)



Agent就是一个工具包，里面包含很多工具,实际起作用的是这些工具。至于这些工具怎么去协作完成一件事，交给LLM思考。

在日常生活中，我们会发现一个工具包做的事情会向下兼容，例如一个货车Agent,这个货车的主要功能是拉货，但是货车除了拉货，也可以拉人，只是拉人并不能发挥其最大功用而已。

所以，就存在这种模糊的界限。在实际开发中,我们要给Agent明确的职责，只做它职责范围内的事情，即使它也能做其他事情。宁可再做一个Agent也不让它做。