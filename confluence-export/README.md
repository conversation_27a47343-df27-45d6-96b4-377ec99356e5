# Confluence 导出索引

导出时间: Tue Jul 08 14:03:12 CST 2025

## 导出的页面

- [㊃_交易生产组](./㊃_交易生产组_6370644.md)
- [4.1_组内概要](./4.1_组内概要_65497833.md)
- [4.1.2_生产资源](./4.1.2_生产资源_65498640.md)
- [4.6.1_能力模型](./4.6.1_能力模型_73435030.md)
- [AAA_首选项!!--线上问题排查路径](./AAA_首选项!!--线上问题排查路径_18612613.md)
- [场景1_Mysql_服务_CPU升高](./场景1_Mysql_服务_CPU升高_24618061.md)
- [场景2_接口响应慢，网络超时](./场景2_接口响应慢，网络超时_24618062.md)
- [1.对外订单结构文档](./1.对外订单结构文档_65473174.md)
- [B2C订单管理系统（中转平台）](./B2C订单管理系统（中转平台）_38840525.md)
- [Maven依赖版本约定](./Maven依赖版本约定_6378645.md)
- [Mysql创表原则](./Mysql创表原则_57784589.md)
- [OSS存储规范](./OSS存储规范_65475473.md)
- [XXL-JOB_最佳实践](./XXL-JOB_最佳实践_65473067.md)
- [谷歌_Java_风格指南](./谷歌_Java_风格指南_18618001.md)
- [AI_相关](./AI_相关_57786510.md)
- [SDK对接指南](./SDK对接指南_65496713.md)
- [####_提测清单模板_####](./####_提测清单模板_####_73433653.md)
- [####_CaseStudy范例_####](./####_CaseStudy范例_####_73433672.md)
- [新零售一期上线切流复盘](./新零售一期上线切流复盘_6371595.md)
- [【20250618】B2C追溯码录入提测清单](./【20250618】B2C追溯码录入提测清单_73445964.md)
- [【20250325】评级中台一期](./【20250325】评级中台一期_65480898.md)
- [【20250619】门店评价一心助手回复接口](./【20250619】门店评价一心助手回复接口_73434812.md)
- [【20241224】美团客服对接](./【20241224】美团客服对接_55417695.md)
- [【20250603】O2O_下账改造](./【20250603】O2O_下账改造_73443449.md)
- [【2025-04-09】配置中心一期](./【2025-04-09】配置中心一期_65487962.md)
- [【20241205】交易中心技术方案调研](./【20241205】交易中心技术方案调研_50317302.md)
- [【20250312】_微商城购物车切换交易中台](./【20250312】_微商城购物车切换交易中台_65474673.md)
- [【20250401】微商城购物车_结算_下单](./【20250401】微商城购物车_结算_下单_65484690.md)
- [【20231114】保山医保技术方案](./【20231114】保山医保技术方案_6368065.md)
- [【20250106】支付中台总体设计](./【20250106】支付中台总体设计_57777779.md)
- [【20250114】_支付中台-B2B支持-余额支付](./【20250114】_支付中台-B2B支持-余额支付_57778152.md)
- [【20250408】低代码平台,支付配置迁移方案](./【20250408】低代码平台,支付配置迁移方案_65487128.md)
- [【20250515】微信支付生产配置数据清理](./【20250515】微信支付生产配置数据清理_73434965.md)
- [接口中台重构_V1.0](./接口中台重构_V1.0_24609530.md)
- [【20240928】_WebSocket_打印](./【20240928】_WebSocket_打印_46269950.md)
- [打印助手交互](./打印助手交互_6371634.md)
- [门店打印插件安装情况和解决方法参考](./门店打印插件安装情况和解决方法参考_6362464.md)
- [【2025-04-24】采购中台一期](./【2025-04-24】采购中台一期_65496791.md)
- [【20250520】_风控中台一期](./【20250520】_风控中台一期_73437574.md)
- [deepseek](./deepseek_57786512.md)
- [SQL注意事项](./SQL注意事项_18615372.md)
- [订单分库分表算法逻辑](./订单分库分表算法逻辑_57783110.md)
- [JDK8-JDK21_新特性汇总](./JDK8-JDK21_新特性汇总_65494476.md)
- [Jackson的多态反序列化](./Jackson的多态反序列化_24613490.md)
- [Retrofit2实现最优雅的网络请求](./Retrofit2实现最优雅的网络请求_24621982.md)
- [SpringBoot中的多线程开发](./SpringBoot中的多线程开发_18621514.md)
- [代码里的兜底策略](./代码里的兜底策略_18614023.md)
- [优化系统](./优化系统_18614011.md)
- [RocketMQ的实践指南](./RocketMQ的实践指南_46284096.md)
- [kafka扩分区&调整canal配置](./kafka扩分区&调整canal配置_65483974.md)
- [xxl-job_版本升级至_2.4.1](./xxl-job_版本升级至_2.4.1_32331583.md)
- [优雅发布-grey-spring-lib维护](./优雅发布-grey-spring-lib维护_24609751.md)
- [对接电商平台流程文档](./对接电商平台流程文档_65483289.md)
- [空接口自动生成mock数据](./空接口自动生成mock数据_65486791.md)
- [订单权限组件](./订单权限组件_73456376.md)
- [订单通用能力沉淀](./订单通用能力沉淀_57780611.md)
- [【20231213】优雅发布改造方案](./【20231213】优雅发布改造方案_6375796.md)
- [【20240118】超时配置上线](./【20240118】超时配置上线_15602463.md)
- [【20240208】网关内存泄漏排查](./【20240208】网关内存泄漏排查_18615032.md)
- [【20240223】SpringCloudGateway升级-_actuator_prometheus](./【20240223】SpringCloudGateway升级-_actuator_prometheus_18619124.md)
- [【20240325】rocketmq_停止消费](./【20240325】rocketmq_停止消费_24612400.md)
- [【20240410】xxl-job_迁移](./【20240410】xxl-job_迁移_24619445.md)
- [【20240425】历史MQ生产消费关系](./【20240425】历史MQ生产消费关系_24623075.md)
- [【20240511】businesses-gateway升级版本](./【20240511】businesses-gateway升级版本_24615106.md)
- [【20240514】网关路由禁用_actuator_shutdown](./【20240514】网关路由禁用_actuator_shutdown_24635386.md)
- [【20240516】api-gateway优化措施](./【20240516】api-gateway优化措施_24637521.md)
- [【20240521】api-gateway分流版本](./【20240521】api-gateway分流版本_31201893.md)
- [【20241202】接口超时治理](./【20241202】接口超时治理_50315835.md)
- [【20241219】慢接口处理（超过9S）](./【20241219】慢接口处理（超过9S）_55415549.md)
- [【20250219】网关升级SpringBoot版本](./【20250219】网关升级SpringBoot版本_57795745.md)
- [【20250313】hydee-middle-order下线_能力沉淀重写](./【20250313】hydee-middle-order下线_能力沉淀重写_65487972.md)
- [【20250603】businesses-gateway网关优化](./【20250603】businesses-gateway网关优化_73443301.md)
- [【20250609】sonar扫描接入](./【20250609】sonar扫描接入_73445413.md)
- [2024-06-04_.net重构-抖店](./2024-06-04_.net重构-抖店_24631643.md)
- [2024-06-05_.net重构-MQ规范对齐](./2024-06-05_.net重构-MQ规范对齐_32314276.md)
- [2024-06-17_.net重构-饿了么](./2024-06-17_.net重构-饿了么_32321802.md)
- [2024-07_.net重构-微商城、包装费下账、毛利预警](./2024-07_.net重构-微商城、包装费下账、毛利预警_38832675.md)
- [2024-08_.net重构-配送服务](./2024-08_.net重构-配送服务_38841095.md)
- [2024-08-13_.net重构-京东到家](./2024-08-13_.net重构-京东到家_38842123.md)
- [2024-08-14_.net重构-京东到家线上问题修复](./2024-08-14_.net重构-京东到家线上问题修复_38842849.md)
- [2024-09-12_抖店B2C](./2024-09-12_抖店B2C_38856623.md)
- [2024-10-08_美团迁移](./2024-10-08_美团迁移_46270962.md)
- [心云OMS客户端安装教程](./心云OMS客户端安装教程_6366618.md)
- [科传打印小票异常问题情况汇总](./科传打印小票异常问题情况汇总_6366304.md)
- [科传打印小票方法](./科传打印小票方法_6368850.md)
- [门店打印驱动安装](./门店打印驱动安装_6366289.md)
- [【20240511】SpringGateway接受外部请求](./【20240511】SpringGateway接受外部请求_24633517.md)
- [【20240511】api-gateway启动过程,确认是否了使用tomcat作为容器-否](./【20240511】api-gateway启动过程,确认是否了使用tomcat作为容器-否_24633361.md)
- [网关token生成](./网关token生成_18625449.md)
- [业务网关排查](./业务网关排查_24613771.md)
- [网关配置-待讨论](./网关配置-待讨论_18615455.md)
- [0.日常运维问题收集](./0.日常运维问题收集_73450226.md)
- [1.技术预研](./1.技术预研_73450221.md)
- [2.订单AI助手-TODO](./2.订单AI助手-TODO_73452478.md)
- [3.订单助手开发](./3.订单助手开发_73457930.md)
- [京东到家](./京东到家_6375951.md)
- [美团闪购](./美团闪购_6375948.md)
- [饿了么](./饿了么_6375953.md)
- [1.企微群内聊天记录获取](./1.企微群内聊天记录获取_73450223.md)
- [2.Spring_AI_Alibaba](./2.Spring_AI_Alibaba_73450829.md)
- [3.AI_Agent](./3.AI_Agent_73454323.md)
- [4.Spring_Al_Alibaba_JManus源码阅读](./4.Spring_Al_Alibaba_JManus源码阅读_73454318.md)
- [5.Dify](./5.Dify_73455300.md)
- [3.1_订单助手知识库](./3.1_订单助手知识库_73458058.md)
- [1.订单下账问题](./1.订单下账问题_73458208.md)
