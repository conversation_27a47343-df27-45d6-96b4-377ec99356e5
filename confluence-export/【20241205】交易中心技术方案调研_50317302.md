# 【20241205】交易中心技术方案调研

### 一、概念

 1. 交易：购物车， 结算页，（调用支付），提单

 交易是一种行为，它指的是买家在某一特定时间，以某一特定价格，从卖家或平台购买一个或多个商品的过程。这种行为在现实世界中表现为价值的直接交换，如买家支付现金或电子支付，卖家则交付商品。而在系统中，这种行为则通过订单来记录和实现。

 2. 订单： 

 订单是交易行为的契约化表现。它详细记录了买家和卖家之间的交易内容，包括商品信息、价格、数量、交付方式、支付方式等。这种契约要求买卖双方必须按照订单的内容进行履约。以实物商品交易为例，买家需要支付订单金额，而卖家则需要按照订单要求将商品寄送给买家。

### 二、交易涉及的模型

1. **买家**：
  - 买家是交易的发起者，他们根据自己的需求和预算，在平台上选择并购买商品。
  - 买家需要注册账号，提供个人信息和支付方式，以便在交易过程中进行身份验证和支付。
2. 买家是交易的发起者，他们根据自己的需求和预算，在平台上选择并购买商品。
3. 买家需要注册账号，提供个人信息和支付方式，以便在交易过程中进行身份验证和支付。
4. **卖家/平台**：
  - 卖家是商品的提供者，他们将自己的商品信息发布在平台上，等待买家购买。
  - 平台则是连接买家和卖家的桥梁，它提供商品展示、交易撮合、支付处理、物流配送等一站式服务。
5. 卖家是商品的提供者，他们将自己的商品信息发布在平台上，等待买家购买。
6. 平台则是连接买家和卖家的桥梁，它提供商品展示、交易撮合、支付处理、物流配送等一站式服务。
7. **订单**：
  - 订单是交易的核心，它记录了买家和卖家之间的交易详情。
  - 订单状态会随着交易的进行而发生变化，如待支付、已支付、待发货、已发货、已完成等。
8. 订单是交易的核心，它记录了买家和卖家之间的交易详情。
9. 订单状态会随着交易的进行而发生变化，如待支付、已支付、待发货、已发货、已完成等。


### 三、交易的流程

1. **商品选择**：买家在平台上浏览商品，选择心仪的商品并加入购物车。
2. **订单生成**：买家确认购物车中的商品后，提交订单并填写收货地址等信息。
3. **支付处理**：买家选择支付方式并完成支付，平台确认支付成功后将订单状态更新为已支付。
4. **物流配送**：卖家根据订单信息准备商品并发货，平台提供物流跟踪服务。
5. **确认收货**：买家收到商品后确认收货，平台将订单状态更新为已完成。
6. **售后服务**：如买家对商品不满意或遇到问题，可通过平台与卖家进行沟通和协商，进行退换货等售后服务


 

![交易流程](./resources/交易流程.png)



### 四、交易整体架构

 **关注交易的核心流程，负责订单数据以及状态流转；兼容上层多样性的业务**

 

![交易中心依赖关系](./resources/交易中心依赖关系.png)



**基础服务、营销相关、交易周边服务 已有对应的服务中台沉淀。交易中心可依赖已有服务进行能力整合，支持多业务接入。**

todo：交易中心是否存订单模型的优劣？

**

![交易架构图](./resources/交易架构图.png)

**

### 六、主要模块

1.购物车

a. 购物车功能

**

![购物车功能图](./resources/购物车功能图.png)

**

b. 购物车结构

todo：弱化购物车门店？

**

![购物车结构](./resources/购物车结构.png)

**

**缓存设计： redis 中使用 json 存储**

主要存储数据

| 字段名称 | uid | skuid | addprice | count | addTime | modifyTime | selected | activityId |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 字段说明 | 用户ID | 商品ID | 加购价格 | 购买数量 | 加购时间 | 修改时间 | 是否选中 | 活动ID |


**缓存结构**

| cart:1001 -> [{"count": 2, "addprice": 99.99, "shopCode": "A002"}] |


模拟 用户 加购 99 件商品 购物车情况

|  | hash | json | 差距 |
| --- | --- | --- | --- |
| 未压缩 | ``` 21031 ``` | ``` 17662 ``` | 3369 |
| 压缩后 | 20271 | 16876 | 3395 |
| 压缩差距 | 760 | 786 |  |


| hash | json |
| --- | --- |
| 1. 结构清晰，以商品id为hash key 值，其余信息为value值。 2. 可快速统计商品总种类数 3. 对于类似于选中 此类操作，只需根据选中 skuid 拿出对应信息即可。 | 1. 相比于hash 占用磁盘容量小 2. 任何操作都需要全量拿出 |


**c.购物车流程**



![购物车操作流程](./resources/购物车操作流程.png)



2. 结算页流程



![结算流程](./resources/结算流程.png)



3. 提单流程



![整体流程](./resources/整体流程.png)



3.1 资源占用

 

![资源占用流程](./resources/资源占用流程.png)



4、退款



![退款操作流程](./resources/退款操作流程.png)



### 七、接口

| 接口 | 功能 | 开始时间 | 结束时间 | 开发周期 | 负责人 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 购物车查询 | 查询当前用户购物车信息 |  |  | 5h |  | 核心在于计算流程，设计公共计算模型 |
| 添加商品 | 购物车添加商品 |  |  | 4h |
| 删除商品 | 购物车中商品删除 |  |  | 4h |
| 修改商品数量 | 购物车中单一商品的数量增减 |  |  | 4h |
| 清空购物车 | 清空购物车 |  |  | 4h |
| 立即购买 | 立即下单购买 |  |  | 4h |
| 活动促销切换 | 购物车中对于参与活动切换，算价重新处理 |  |  |  |  |
| 选中商品 | 购物车中选择商品 |  |  |  |  |
| 购物车结算 | 对选中的商品进行结算操作 |  |  |  |  |
| 修改收货地址 | 用户结算页修改收货地址 |  |  |  |  |  |
| 修改优惠券 | 用户结算页选择优惠券 |  |  |  |  |  |
| 提单 | 用户结算页下单 |  |  |  |  | 核心流程，牵扯资源占用及多系统操作 |
| 支付 | 下单后发起支付流程 |  |  |  |  |  |
| 支付结果回调 | 接收支付结果信息 |  |  |  |  |  |
| 取消支付 | 用户取消未支付的订单 |  |  |  |  |  |
| 用户申请退款 | 用户发起退款 |  |  |  |  |  |
| 退款审核通过 | 退款审核通过后请求支付退款及资源释放 |  |  |  |  |  |