# 饿了么

## 1.开放平台地址: https://open-retail.ele.me/#/apidoc

## 2.接口系统级参数

| **请求参数** | **参数类型** | **参数名称** | **是否必传** | **备注** | **参数示例** |
| source | String | AppID | 是 | 创建App时 ，零售开放平台会为每个App生成一个AppID和与之对应的secretKey | 37641616 |
| cmd | String | API接口名称 | 是 | 参见[API文档](https://open-retail.ele.me/#/apidoc)，获取方式参见此文档1.2.3 | order.get |
| timestamp | String | 请求时间戳 | 是 | 可以根据调用API的需求带上作为时间戳校验依据，格式为时间转换为秒值，也就是从1970年1月1日起至今的时间转换为秒，注意长度是10位 | 1590983652 |
| version | String | API接口版本 | 是 | API接口版本用于接口升级等场景，默认值：3 | 3 |
| ticket | String | 请求唯一标识 | 是 | 接口请求唯一标识用于问题排查，链路跟踪等使用。ticket的生成规则参见此文档1.2.4 | 8C7D975C-9E9B-F8AB-0D8A-D1B5E3ECF786 |
| sign | String | 请求签名 | 是 | 其值是根据申请得到的SecretKey和本次请求的url共同生成，请参考 [API签名规则](https://open-retail.ele.me/#/guide?topic=gigkg3)。 如果API不需要签名则可以不带入此参数 | af2fe13af77263bab08901cbb11114f7 |
| encrypt | String | 加密方式 | 否 | 新接口已不支持请求数据加密，为了兼容历史版本，该字段保留，默认值：aes | aes |
| access_token | String | 访问令牌 | 否 | 此为非必传字段，不传此参数也可正常访问接口。如需该参数请按照示例格式传递并参与签名，不可传空。 | 6b7c0d4f-82c4-4577-a461-e06df21 |


## 3.oms业务流转

### 3.1 Item

| 名称 | HemsMethod | .NET API | 饿了么 | 备注 |
| --- | --- | --- | --- | --- |
| 批量创建商品 | hems.cloud.item.batch.create | api/Item/ItemsCreate | sku.create |  |
| 商品批量上架 | hems.cloud.item.batch.online | api/Item/ItemsOnline | sku.online |  |
| 商品批量下架 | hems.cloud.item.batch.offline | api/Item/ItemsOffline | sku.offline |  |
| 创建商品类别 | hems.cloud.item.category.add | api/Item/CategoryCreate | sku.shop.category.create |  |
| 商品类别删除 | hems.cloud.item.category.delete | api/Item/CategoryDelete | sku.shop.category.delete |  |
| 获取商品分类列表信息 | hems.cloud.item.category.get | api/Item/CategoryGet | sku.shop.category.get |  |
| 价格批量同步 | hems.cloud.price.batch.sync | **api/Item/PriceBatchSync** | sku.price.update.batch |  |
| 库存批量同步 | hems.cloud.quantity.batch.sync | api/Item/QuantityBatchSync | sku.stock.update.batch |  |
| 批量更新商家商品编码 | hems.cloud.item.update.outskuid | api/Item/ItemOutSkuidsUpdate | sku.shop.customsku.map |  |
| 获取商品信息 | hems.cloud.item.get.list | api/Item/ItemsGet | sku.list |  |


### 3.2 Order

| 名称 | HemsMethod | .NET API | 饿了么 | 备注 |
| --- | --- | --- | --- | --- |
| 订单接单 | hems.cloud.order.confirm | api/Order/ConfirmOrder | order.confirm |  |
| 拣货完成 | hems.cloud.order.pickcomplete | api/Order/OrderPickComplete | order.pickcomplete |  |
| 订单发货 | hems.cloud.order.delivery | api/Order/OmsOrderDelivery | order.sendout | 目前不使用，统一使用 hems.cloud.order.riderstatus.sync |
| 订单妥投 | hems.cloud.order.delivery.end | api/Order/OrderDeliveryEnd | order.complete | 目前不使用，统一使用 hems.cloud.order.riderstatus.sync |
| 订单转自配送 | hems.cloud.order.change.self | api/Order/OrderChangeSelf | order.switchselfdelivery |  |
| 同步骑手状态到平台 | hems.cloud.order.riderstatus.sync | api/Order/OrderRiderStatusSync | order.selfDeliveryStateSync | 自配送模式下使用， |
| 呼叫骑手 | hems.cloud.order.calldelivery | api/Order/CallDelivery | order.callDelivery |  |
| 订单取消 | api/Refund/OrderCancel | **api/Refund/OrderCancel** | order.reverse.apply | 订单状态变更 |
| 发起部分退款 | hems.cloud.order.applypartrefund | api/Refund/OrderApplyPartRefund |  | 目前平台不支持，只能由客户发起 |
| 同意订单退款申请 | hems.cloud.order.refund.agree | api/Refund/OrderRefundAgree | order.reverse.process |  |
| 同意订单退款审核 | hems.cloud.order.refund.audit.agree | api/Refund/OrderRefundAgree | order.reverse.process |  |
| 拒绝订单退款审核 | hems.cloud.order.refund.audit.reject | api/Refund/OrderRefundReject | order.reverse.process |  |
| 拒绝订单退款申请 | hems.cloud.order.refund.reject | api/Refund/OrderRefundReject | order.reverse.process |  |
| 订单补单 | hems.cloud.order.repair | api/Order/RepairO2oOrder | order.get | 补单接口实际调用order.get 获取订单详情推送MQ 创建订单 |
|  |  |  |  |  |


# 3.订单类API

## 3.1 查看订单列表

此接口主要用于获取门店下筛选时间段内的订单数据列表场景，也可以用于防止漏单和补单场景
1仅可获取到绑定api后发生的订单数据；
2仅可查询近3个月内的订单；
3入参开始时间start_time 和结束时间end_time，以订单创建时间为准可圈定范围搜索
注意：为了提升商家的接入效率，保障订单接入流程的完整性请在接口接入前先查看 订单[接入方案](https://open-retail.ele.me/#/guide?topic=wfef77)

需要授权接口限流50次/source/秒

### 3.1.1 接口地址: https://api-be.ele.me/me.ele.retail:order.list-3

### 3.1.2 入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| body | message:me.ele.nop.doa.api.param.request.order.OrderListReqDto | 是 | 请求消息体 | 见消息体 |
| baidu_shop_id | java.lang.String | 否 | 平台生成的门店ID | 42267022815 |
| end_time | java.lang.String | 否 | 结束时间，不传默认只查询当天订单 | 1618848000 |
| page | int | 否 | 订单列表分页返回，该参数指定页数 | 1 |
| page_size | int | 否 | 每页订单数量，固定值：20，不支持自定义 | 20 |
| shop_id | java.lang.String | 否 | 合作方门店ID（商户系统门店ID） | 3442267 |
| start_time | java.lang.String | 否 | 起始时间，不传默认只查询当天订单 | 1619020800 |
| status | java.lang.String | 否 | 订单状态，请查看[订单状态表](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 9 |


### 3.1.3 出参

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
| errno | Integer | 返回错误码 | 0 |
| error | String | 返回错误信息 | success |
| data | message:me.ele.nop.doa.api.dto.order.list.OrderListResDto | 返回信息 | 见消息体 |
| list | message:me.ele.nop.doa.api.dto.order.list.OrderBasicInfoDto[] | 订单列表 | [] |
| baidu_shop_id | java.lang.String | 平台门店ID | 42267022815 |
| create_time | java.lang.String | 订单创建时间 | 1614518268 |
| order_from | java.lang.String | 订单来源，枚举值： 1 饿了么星选APP， 2 饿了么APP | 2 |
| order_id | java.lang.String | 订单ID | 2148732753322393829 |
| order_status | java.lang.String | 订单状态，请查看[订单状态表](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 9 |
| pay_status | java.lang.String | 支付状态，枚举值：1 未支付，2 已支付 | 1 |
| pay_type | java.lang.String | 支付类型，枚举值：1 线下支付，2 在线支付 | 1 |
| shop_id | java.lang.String | 合作方门店ID（商家系统门店ID） | 267238 |
| user_phone | java.lang.String | 用户手机号 | 13534328765 |
| page | java.lang.String | 当前页，每页默认返回20条数据 | 20 |
| pages | java.lang.String | 页数 | 1 |
| total | java.lang.String | 返回订单总条数 | 200 |


## 3.2 查看订单详情

此接口主要用于订单下单后获取订单详细信息，包括订单、商品、营销商户以及用户信息等。
1. 如订单详情中down_flag和discount_down_flag为1时，标识该订单因网络或信息交互异常被降级，此时订单会被标记为“已降级”状态。此类订单不影响订单履约流程（无需拒单），重试查询获取订单完整数据即可
2. 仅可查询近三个月内的订单
注意：为了提升商家的接入效率，保障订单接入流程的完整性请在接口接入前先查看 订单[接入方案](https://open-retail.ele.me/#/guide?topic=wfef77)

### 3.2.1 接口地址:https://api-be.ele.me/me.ele.retail:order.get-3

### 3.2.2 入参

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| body | message:me.ele.retail.OrderGetReqDto | 是 | 请求参数 | 见消息体 |
| order_id | String | 是 | 订单ID | 2148234997323481317 |


### 3.2.3 出参

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
| errno | String | 返回错误码 | 0 |
| error | String | 返回错误信息 | success |
| data | message:me.ele.nop.doa.api.dto.order.get.OrderGetDataResultDataDto | 响应结果 | 见消息体 |
| discount | message:me.ele.nop.doa.api.dto.order.get.Discount[] | 优惠信息 | [] |
| order | message:me.ele.nop.doa.api.dto.order.get.Order | 订单信息 | {} |
| products | message:me.ele.nop.doa.api.dto.order.get.Product[][] | 订单商品信息数组，数组内第一层数组为分袋（可忽略分袋，但会保留此层级），第二层数组为商品信息 | [] |
| shop | message:me.ele.nop.doa.api.dto.order.get.Shop | 商户信息 | {} |
| source | java.lang.String | 开发者应用id，返回为APPID | 20628846 |
| user | message:me.ele.nop.doa.api.dto.order.get.User | 用户信息 | {} |


#### 1）message:me.ele.nop.doa.api.dto.order.get.Discount[](优惠信息)

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
| rule_id | java.lang.String | 该字段已废弃使用 | 0 |
| shop_rate | java.lang.Integer | 商户承担金额，单位：分 | 1800 |
| logistics_rate | java.lang.Integer | 物流承担金额，单位：分 | 0 |
| fee | java.lang.Integer | 活动优惠总金额（1. 店铺维度活动：为该活动总优惠金额；2. 商品维度活动：为upc维度活动总优惠金额(公式 fee = save_price*activity_product_num)），如A和B商品都享受A活动，将会存在两个discount数组，总的活动优惠金额=A商品和B商品 discount数组中fee的相加总和；3. 各方承担优惠分摊公式：fee=shop_rate+ logistics_rate+ agent_rate+ baidu_rate+ user_rate）， 单位：分 | 1800 |
| activity_id | java.lang.String | 平台生成的活动id | 6000000318500717 |
| agent_rate | java.lang.Integer | 代理商承担金额，单位：分 | 0 |
| baidu_rate | java.lang.Integer | 平台承担金额，单位：分 | 0 |
| type | java.lang.String | 优惠类型，请查看[订单优惠类型](https://open-retail.ele.me/#/guide?topic=ntmt8f) | g_te |
| custom_activity_id | String | 合作方活动ID | 20989045 |
| desc | java.lang.String | 优惠描述 | 一口价活动 |
| products | message:me.ele.nop.doa.api.dto.order.get.DiscountProducts[] | 享受活动的商品信息(UPC维度)，如活动为店铺活动此参数不返回 | [] |
| activity_id | java.lang.String | 活动id | 6000000318500717 |
| activity_product_num | java.lang.Integer | 参与该活动商品数量(upc维度) | 2 |
| baidu_product_id | java.lang.String | 平台生成的商品ID，同商品的sku_id | 16757760440792191 |
| now_price | java.lang.Integer | 单商品优惠后金额，单位：分，计算公式：now_price = orig_price - save_price | 1180 |
| orig_price | java.lang.Integer | 单商品原始金额，单位：分 | 2080 |
| save_price | java.lang.Integer | 单商品优惠金额，单位：分 | 900 |
| user_rate | java.lang.Integer | 用户承担金额，单位：分 | 0 |
| coupon_id | String | 红包/劵id | 2481064471777 |
| activity_type | String | 活动类型[营销活动类型](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 1 |
| activity_child_type | String | 活动子类型[营销活动子类型](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 6 |


#### 2) message:me.ele.nop.doa.api.dto.order.get.Order(订单信息)

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
| atshop_time | java.lang.Long | 骑手到店时间 | 1618933864 |
| business_type | java.lang.String | 取货类型，枚举值：0-外卖到家，1-用户到店自提 | 0 |
| cancel_time | java.lang.String | 订单取消时间 | 1618819637 |
| cold_box_fee | java.lang.Long | 冷链费用，单位：分 | 0 |
| commission | java.lang.Long | 佣金（费用包含佣金（技术服务费）+履约服务费+支付服务费），单位：分 | 314 |
| confirm_time | java.lang.Long | 商家接单时间 | 1618991884 |
| create_time | java.lang.String | 订单创建时间 | 1618991858 |
| delivery_fee | message:me.ele.nop.doa.api.dto.order.get.DeliveryFee | 配送费均摊信息 | {} |
| platform_delivery_fee | java.lang.Integer | 配送费平台承担金额，单位：分 | 0 |
| shop_delivery_fee | java.lang.Integer | 配送费商户承担金额，单位：分 | 430 |
| delivery_fee_discount | Integer | 配送费优惠金额，单位：分 | 0 |
| delivery_party | java.lang.String | 物流类型说明： 2:商家自配送（废弃） 4:蜂鸟众包（已开启自动呼单） 5:蜂鸟平台配送 6:蜂鸟众包（未开启自动呼单）+自配送，两种配送方式二选一，当众包配送异常后，可切自配送。 8:快递配送 9:半日达 10:鸟潮小时达 | 6 |
| delivery_phone | java.lang.String | 骑士手机号 | 13800000000 |
| delivery_time | java.lang.Long | 骑士已取餐时间 | 1618735126 |
| discount_fee | java.lang.Integer | 订单优惠总金额，单位：分 | 2170 |
| down_flag | java.lang.Long | 是否结算降级。枚举值：1 是，0 否；极少数订单因网络或信息交互异常，导致订单部分字段（如订单金额）生成延迟，此时订单会被标记为“已降级”状态，不影响订单正常履约（无需拒单），开发者重新调用order.get订单详情接口获取完整订单数据即可。 | 0 |
| eleme_order_id | java.lang.String | 订单ID，同order_id | 2148661984223510757 |
| expect_time_mode | java.lang.Integer | 送达时间类型，枚举值： 1 定时达， 2 限时达（错峰配送） | 1 |
| ext | message:me.ele.nop.doa.api.dto.order.get.Ext | 订单附加信息，包含订单退款信息及鲜花订单祝福语信息 | {} |
| giver_phone | java.lang.String | 订购人电话 | 13800000000 |
| greeting | java.lang.String | 祝福语 | 生日快乐！ |
| online_cancel_status | java.lang.Integer | 全单退状态（订单为全单退订单时会有此字段）10:发起申请,20:客服介入,30:客服拒绝,40:客服同意,50:商户拒绝,60:商户同意,70:申请失效 | 70 |
| part_refund | java.lang.Integer | 部分退款标识,1商户发起 2用户发起 | 1 |
| part_refund_status | java.lang.Integer | 部分退状态（订单为部分退订单时会有此字段）10表示商家/用户发起部分退款申请 20表示部分退款成功 30用户申请仲裁,客服介入 40表示部分退款失败 50表示商家拒绝用户发起的部分退款申请 | 10 |
| taoxi_flag | java.lang.Integer | 淘系订单标识,0非淘系订单 1淘系订单 | 1 |
| user_cancel | java.lang.Integer | 1表示订单完成前用户全单取消申请流程，2表示订单完成后用户全单退款申请流程 | 1 |
| estimate_invoice_aomout | Integer | 预计开票金额；单位：分，备注：订单完成后返回 | 410 |
| finished_time | java.lang.Long | 订单完成时间 | 1618277188 |
| invoice_title | java.lang.String | 发票抬头 | 信息科技公司 |
| is_cold_box_order | java.lang.Integer | 是否是冷链订单，枚举值： 1是， 0否 | 1 |
| is_private | java.lang.Integer | 是否匿名订单，枚举值：1是， 0否 | 1 |
| latest_send_time | java.lang.Long | 用户期望最晚送达时间，1. 如用户选择的是预定送达时间点，此字段同send_time 2. 如用户选择的是预定送达时间片，此字段为最晚送达时间 | 1619053200 |
| meal_num | java.lang.String | 用户所需餐具数量 | 1 |
| need_invoice | java.lang.Integer | 是否需要发票，枚举值： 1是 ，2否 | 1 |
| order_flag | java.lang.Integer | 订单标识（注意：仅表示发生过部分退款，不代表部分退款成功。），枚举值：0 不是部分退款订单，1 部分退款订单 | 0 |
| order_from | java.lang.String | 订单来源，枚举值： 1 饿了么星选APP， 2 饿了么APP，3 微信小程序 | 2 |
| order_id | java.lang.String | 订单ID | 2148732753322393829 |
| order_index | java.lang.Integer | 订单当日流水号（门店维度，按照送达日的下单顺序生成序号），从1开始递增。注意：通过订单序号可以判断是否出现漏单情况 | 1 |
| package_fee | java.lang.Integer | 包装费，单位：分 | 100 |
| pay_status | java.lang.Integer | 付款状态， 枚举值：1 未支付 ，2 已支付 | 1 |
| pay_type | java.lang.Integer | 付款类型 ，枚举值：1 线下支付，2 在线支付 | 2 |
| pick_up_code | java.lang.String | 用户到店自提取货码。 | csdfe |
| pickup_time | java.lang.Long | 商家拣货完成时间 | 1618276675 |
| remark | java.lang.String | 订单备注，无备注时返回0 | 不要辣 |
| responsible_party | java.lang.String | 取消订单责任承担方，已废弃 | 1 |
| send_fee | java.lang.Integer | 订单配送费，单位：分 | 100 |
| send_immediately | java.lang.Integer | 是否是预约单，枚举值： 1即时单，2预约单 | 1 |
| send_time | java.lang.Long | 用户期望最早送达时间 | 1618277188 |
| shop_fee | java.lang.Integer | 商户应收金额，单位：分 | 100 |
| status | java.lang.Integer | 订单状态，请查看[订单状态表](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 9 |
| taxer_id | java.lang.String | 纳税人识别号 | 1093478343792 |
| total_fee | java.lang.Integer | 订单总金额，单位：分 | 2000 |
| user_fee | java.lang.Integer | 用户实付金额，单位：分 | 1000 |
| is_prescription | Integer | 是否处方药订单，枚举值： 0 不是 ，1 是 | 1 |
| third_pl_info | message:me.ele.nop.doa.api.dto.order.get.ThirdPLInfo | 第三方运力数据 | {} |
| pick_up_code | String | 拣货码 | 573587 |
| token | String | 令牌 | 157757007020061426 |
| merchant_pick_time | Long | 最晚拣货时间 | 1618277188 |
| third_order_id | String | 三方单号(同城履约单id) | 183241035222102422 |
| routeNodes | String | 流向信息 | [{\"merchantCode\":\"CSYIMA\",\"nodeCode\":\"测试波次达入饿澳门\",\"nodeIndex\":2,\"nodeType\":\"CFC\"},{\"merchantCode\":\"CSYIMA\",\"nodeCode\":\"am配送站001\",\"nodeIndex\":4,\"nodeType\":\"DELIVERY_DOCK\"}] |
| site_area_code | String | 路区代码code | 60055951 |
| site_area_name | String | 路区名称 | 测试路区全覆盖 |
| merchant_total_fee | Integer | 商户应收包装费，单位：分 | 10 |
| promise_out_time | Long | 承诺出餐时间 | 1618277188 |
| picking_time | Integer | 拣货时长 分钟 | 10 |
| medicare_fee | Integer | 医保支付金额 ，单位：分 | 10 |
| out_stock_option_text | String | C端缺货处置方案 | 缺货时电话与我联系 |
| expensive_product_pickup_code | String | 贵品取货码 | ISOQDFB |
| expensive_product_receving_code | String | 贵品签收码 | NSLDASJK |
| three_card_no | String | 第三方会员卡号 | 12222111 |
| shop_card_fee | message:ShopCardFee | 购物卡金额信息 | {} |
| origin_merchant_commission_amount | Long | 佣金，技术服务费，单位：分 | 100 |
| base_logistics_amount | Long | 履约服务费，单位：分 | 100 |
| pay_channel_fee | Long | 支付服务费，单位：分 | 100 |
| discount_down_flag | Long | 营销是否降级;1:是,0:否;极少数订单因网络或信息交互异常，导致订单营销数据生成延迟，此时订单会被标记为“已降级”状态，需开发者重新调用查看订单详情接口获取完整订单数据。 | 0 |
| pay_time | Long | 订单支付时间 | 1618277188 |
| self_take_place | String | 自提点地址 | 澳门威尼斯人 |
| self_take_code | String | 自提点编码 | 000 |
| last_delivery_time | Long | 最晚发货时间 | 1618277188 |
| merchant_hudan_delivery_fee | Long | 呼单配送费,单位：分 | 100 |
| crowd_hudan_tip | Long | 呼单小费,单位：分 | 100 |
| shop_brand_id | String | 品牌店铺名称 | 全能超市 |
| send_fee_detail | message:me.ele.newretail.order.api.client.model.dto.SendFeeDetail | 配送明细信息 | {} |
| service_tags_desc | String[] | 订单享有服务标签，枚举值：行业慢必赔、商家慢必赔、平台慢必赔、融化必赔 | {商家慢必赔, 融化必赔} |
| is_shop_presell | Integer | 是否店发预售订单，枚举值： 0 否，1 是 | 1 |
| package_fee_detail | message:me.ele.newretail.order.api.client.model.dto.pkg.ApiPackageFeeDetail | 包装费明细信息 | {} |
| invoice_type | Integer | 发票类型 | 2=纸质发票 4=电子发票 |
| invoice_address | String | 发票地址：纸质发票是用户的收货地址，电子发票是email地址 | [<EMAIL>](mailto:<EMAIL>) |
| wechat_pay_id | String | 微信支付单号：备注只有order_from = 3 的时候才会返回 | 4200001908202307315692886900 |
| product_order_no | String | 外部订单号 | 3243214231434123432 |
| fulfill_ext | String | 半日达履约服务标签 | 无 |
| is_medicare_order | Integer | 是否医保支付订单，枚举值： 0/null 否，1 是 | 1 |


#### 3)message:me.ele.nop.doa.api.dto.order.get.Product[][](订单商品信息数组，数组内第一层数组为分袋（可忽略分袋，但会保留此层级），第二层数组为商品信息)

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
| activity_id | java.lang.String | 平台生成的活动id，已废弃，不返回 | 6000000318500717 |
| baidu_product_id | java.lang.String | 平台生成的商品ID，同商品的sku_id | 16757760440792191 |
| custom_sku_id | java.lang.String | 合作方商品ID;未设置时为空;同商品的custom_sku_id | 5102699 |
| gm_ids | String[] | 赠品关联的主品的子订单 ID List，代表主品unique_id，格式： ["id1","id2”,……] | ["844749281870177526","844749281870177123"] |
| is_free_gift | java.lang.Integer | 是否为赠品；1 是赠品，0或null 不是赠品 | 0 |
| package_amount | java.lang.Integer | 商品包装数量 | 1 |
| package_fee | java.lang.Integer | 商品包装费，单位：分 | 100 |
| package_price | java.lang.Integer | 商品包装费，单位：分 | 100 |
| prescription_id | java.lang.String | 处方药编号; 不存在时为空“” | 1347589347343 |
| product_amount | java.lang.Integer | 商品数量 | 3 |
| product_attr | message:me.ele.nop.doa.api.dto.order.get.ProductAttr[] | 商品参数，已废弃，返回数据为空 | {} |
| baidu_attr_id | java.lang.Long | 属性ID | 1703572074 |
| name | java.lang.String | 属性名称 | 辣的 |
| option | java.lang.String | 属性 | 微辣 |
| product_custom_index | java.lang.String | 商品唯一串，已废弃 | 1529731460_0_0 |
| product_features | message:me.ele.nop.doa.api.dto.order.get.ProductFeatures[] | 商品属性和加工服务 | [{"name":"加工类型","baidu_feature_id":0,"option":"切块"},{"name":"甜度","baidu_feature_id":0,"option":"少糖"}] |
| baidu_feature_id | java.lang.Long | ID | 0 |
| name | java.lang.String | 名称，1. 商品属性展示商品属性名称 2. 加工服务展示为固定值“加工类型” | 温度/加工类型 |
| option | java.lang.String | 属性/加工类型 | 常温/切块 |
| product_fee | java.lang.Integer | 商品总价，单位：分 | 300 |
| product_name | java.lang.String | 商品名称 | 西瓜 |
| product_price | java.lang.Integer | 商品单价，单位：分 | 100 |
| product_subsidy | message:me.ele.nop.doa.api.dto.order.get.ProductSubsidy | 订单中商品享受的优惠活动信息（upc维度） | [] |
| agent_rate | java.lang.Integer | 代理商承担金额，单位：分 | 0 |
| baidu_rate | java.lang.Integer | 平台承担金额，单位：分 | 100 |
| discount | java.lang.Integer | 商品总优惠金额，单位：分，计算公式：discount= agent_rate+baidu_rate+logistics_rate+shop_rate+user_rate | 300 |
| discount_detail | message:me.ele.nop.doa.api.dto.order.get.DiscountDetail[] | 订单中商品享受的优惠活动信息明细（upc维度） | {} |
| shop_rate | java.lang.Integer | 商户承担金额，单位：分 | 100 |
| activity_id | java.lang.String | 商品参与活动id | 6000000318500717 |
| baidu_rate | java.lang.Integer | 平台承担金额，单位：分 | 100 |
| type | java.lang.String | [订单优惠类型](https://open-retail.ele.me/#/guide?topic=ntmt8f) | g_te |
| custom_activity_id | String | 合作方活动ID | 673490 |
| coupon_id | String | 红包/券id | 2481064471555 |
| activity_type | String | 活动类型[营销活动类型](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 1 |
| activity_child_type | String | 活动子类型[营销活动子类型](https://open-retail.ele.me/#/guide?topic=ntmt8f) | 6 |
| logistics_rate | java.lang.Integer | 物流承担金额，单位：分 | 0 |
| shop_rate | java.lang.Integer | 商户承担金额，单位：分 | 200 |
| user_rate | java.lang.Integer | 用户承担金额，单位：分 | 0 |
| product_type | java.lang.Integer | 商品类型，枚举值：1 单品 ，2 套餐 ，3 配料 | 1 |
| sub_biz_order_id | java.lang.String | 子订单ID，可区分同商品ID的不同属性；订单逆向操作必传字段 | 1736472206112319631 |
| supply_type | java.lang.Integer | 配送类型，枚举值：0代表普通配送，1代表冷链配送 | 1 |
| total_fee | java.lang.Integer | 订单中商品总金额，单位：分 | 300 |
| total_weight | java.lang.Integer | 商品总重量，单位：克 | 200 |
| upc | java.lang.String | 商品upc | 6952426501123 |
| weight_type | java.lang.Integer | 是否是称重品。枚举值：2 非称重品，1和3 称重品 | 2 |
| properties | message:me.ele.nop.doa.api.dto.order.get.Properties[] | 多规格属性信息 | [] |
| name | String | 属性名 | 度数 |
| value | String | 属性值 | 400 |
| customSkuSpecId | String | 多规格合作方商品skuId | 568349002 |
| subState | String | 子单状态ID 1:等待接单 5:商户已接单 9:订单已完成 10:子订单已取消 -1:未知状态 | 1 |
| combine_products | message:CombineProduct[] | 组合商品子商品信息 | [] |
| baidu_product_id | String | 平台生成的商品ID，同商品的sku_id | 16757760440792191 |
| product_name | String | 商品名称 | 瓜子 |
| custom_sku_id | String | 合作方商品ID;未设置时为空;同商品的custom_sku_id | 5102699 |
| product_amount | Integer | 商品数量 | 1 |
| upc | String | 商品upc | 6952426501123 |
| total_weight | Integer | 商品总重量，单位：克 | 132 |
| sku_spec_id | String | 多规格商品ID，非多规格为空 | 57382942 |
| fulfill_ext | String | 半日达履约服务标签 | 无 |
| is_medicare_drug | Integer | 是否是医保品，1代表是，0或null代表不是 | 1 |
| shelf_position | String | 货架号 | 1-1-2 |
| service_tags | String[] | 商品服务标签["不支持7天无理由","7天无理由退货（包装破损不退）","7天无理由退货","融化必赔","保温","缺重退差"] | ["7天无理由退货","融化必赔","保温"] |


#### 4)message:me.ele.nop.doa.api.dto.order.get.Product[][](订单商品信息数组，数组内第一层数组为分袋（可忽略分袋，但会保留此层级），第二层数组为商品信息)