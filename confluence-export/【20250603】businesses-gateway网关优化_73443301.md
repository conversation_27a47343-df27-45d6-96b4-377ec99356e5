# 【20250603】businesses-gateway网关优化

Green已完成

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5767

## 项目:

businesses-gateway

## 分支:

refactor-20250522-网关优化

## 背景

之前businesses-gateway网关在遭遇流量洪峰时会重启

## 分析

经过AI分析得出原因是因为网关的过滤器逻辑中使用了同步逻辑导致。因为Spring Cloud Gateway 底层是基于 **Netty** 和 **Reactor 异步非阻塞模型** 构建的,如果在过滤器中使用了同步调用，会阻塞 Reactor 的工作线程（通常称为 Event Loop 线程）。一旦Reactor的工作线程被阻塞会导致以下问题:

- 整个事件循环被挂起，不能继续处理其他请求
- 吞吐量下降，响应变慢，网关处理能力大幅降低
- JVM 的 GC 压力加大。系统负载升高


因为不能处理其他请求,k8s的健康检查探针探测到服务异常,就会杀死网关服务。

### 阻塞代码分析方法

借助开源项目 [BlockHound](https://github.com/reactor/BlockHound) 来检查阻塞代码。 [BlockHound文档](https://github.com/reactor/BlockHound/blob/master/docs/quick_start.md) 

使用BlockHound检测出了项目中主要是同步Redis阻塞了工作线程。另外还有动态路由的nacosDiscoveryClient调用是同步的

## 优化方案

1. **主要**: 同步代码修改为响应式，使用ReactiveRedis来替代同步Redis
2. **次要**: 移除无效代码,主要是网关侧的付费资源判断逻辑,可以最多可以节省2次网络请求。
  1. 判断付费资源的Redis对象使用了Jackson2JsonRedisSerializer序列化方式,相比StringRedisSerializer来说整体成本较高且移植性不好
3. 判断付费资源的Redis对象使用了Jackson2JsonRedisSerializer序列化方式,相比StringRedisSerializer来说整体成本较高且移植性不好
4. 代码整理。移除无效代码,减少过滤器中的无效逻辑调用。例如: 从请求中解析相关用户信息,最后却什么也不做、请求耗时统计等


## 过滤器梳理


全局过滤器

| 过滤器 | 作用 | 优化措施 | 结论 |
| --- | --- | --- | --- |
| AccessGatewayFilter | 全局,核心的用户权限校验 | 全是使用异步Redis | 已经优化完成 |
| DynamicsRequestFilter | 动态路由 | 因为是在测试环境使用。生产环境不处理 | 这个过滤器主要是在测试环境启用,故在生产环境不优化 |
| ModifyBodyGatewayFilter |  |  | 无需优化 |
| BodygzipFilter | 当请求是被压缩的,解压为明文,处理好之后再压缩回去给调用方 | - 当无需压缩时(请求头里面没有配置"huditCompressed"),无需优化 - 当需要压缩时,自测后,无需优化 | 无需优化 |
| IdempotentFilter | 重复请求 | 全是使用异步Redis | 已经优化完成、自测完成 |
| RewriteMerCodeFilter | 重写merCode[https://docs.spring.io/spring-cloud-gateway/docs/3.0.3/reference/html/#global-filters](https://docs.spring.io/spring-cloud-gateway/docs/3.0.3/reference/html/#global-filters) | 这个过滤器没有注入到SpringBean,即 无用。 | 无需优化，注释掉 |
| RouteForbiddenFilter | 禁用指定路由 | 无需优化 | 无需优化 |


非全局过滤器

| 过滤器 | 作用 | 优化措施 | 结论 |
| --- | --- | --- | --- |
| YxtLoginAccessGatewayFilterFactory | 一心助手定制的过滤器 |  | 不优化. 和一心助手确认最终会废弃,全部走一心助手的网关。目前这个过滤器的流量在逐渐减少,故不再投入资源在这个过滤器上 |


## 压测方案

因为需要压测网关本身优化效果,需要消除下游服务对网关的测试结果的影响,所以让请求在网关内部闭环，不涉及其他服务。

### 实现:

package cn.hydee.gateway.filter;

import java.nio.charset.StandardCharsets;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;


@Component
public class DummyEndpointFilter implements GlobalFilter, Ordered {

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    String path = exchange.getRequest().getURI().getPath();
    if ("/dummy".equals(path)) {
      byte[] data = "OK".getBytes(StandardCharsets.UTF_8);
      exchange.getResponse().setStatusCode(HttpStatus.OK);
      exchange.getResponse().getHeaders().setContentLength(data.length);
      return exchange.getResponse().writeWith(Mono.just(exchange.getResponse()
          .bufferFactory().wrap(data)));
    }

    return chain.filter(exchange);
  }

  @Override
  public int getOrder() {
    return -1; // 保证早期执行
  }
}



package cn.hydee.gateway.config;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class DummyRouteLocator {

  @Bean
  public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
    return builder.routes()
        .route("dummy_test", r -> r
            .path("/dummy")
            .filters(f -> f
                .addResponseHeader("X-Test", "gateway")
            )
            .uri("no://backend")) // 注意：使用一个不会被解析的uri
        .build();
  }

}



### 压测环境

- 测试环境 、 2个网关节点
- 压测时间选择在晚上11点左右,网关白天供测试部门使用,白天不压测避免压测结果不准


### 压测脚本

为了方便测试不同并发下的网关表现,写了一个脚本,方便数据收集与处理。

- 压测工具使用ab
- 固定请求总数


bash#!/bin/bash

# 响应式优势测试策略 - 带错误处理版本
# 目标：找到响应式Redis相比同步Redis的性能拐点


TOKEN="Authorization: ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
POST_DATA="post_data.json"
# 固定总请求数，测试不同并发级别的性能
# TOTAL_REQUESTS=1
TOTAL_REQUESTS=8000


## local
URL="http://**********:9000/dummy"
RESULT_FILE="reactive_performance_test_local.log"
GATEWAY_HOST="**********"
GATEWAY_PORT="9000"

## dev
# URL="http://businesses-gateway.svc.k8s.dev.hxyxt.com/dummy"
# RESULT_FILE="k8s_reactive_performance_dev.log"
# GATEWAY_HOST="businesses-gateway.svc.k8s.dev.hxyxt.com"
# GATEWAY_PORT="80"

## test
# URL="http://businesses-gateway.svc.k8s.test.hxyxt.com/dummy"
# GATEWAY_HOST="businesses-gateway.svc.k8s.test.hxyxt.com"
# GATEWAY_PORT="80"
# 同步redis
# RESULT_FILE="k8s_reactive_performance_test_sync.log"
# 响应式redis
# RESULT_FILE="k8s_reactive_performance_test_reactive.log"
# 响应式redis + 动态线程池
# RESULT_FILE="k8s_reactive_performance_test_reactive_bound.log"




echo "响应式Redis性能测试开始..." | tee $RESULT_FILE
echo "测试时间: $(date)" | tee -a $RESULT_FILE
echo "========================================" | tee -a $RESULT_FILE



# 测试函数 - 增强错误处理
run_test() {
    concurrency=$1
    requests=$2
    test_name=$3
    
    echo "[$test_name] 并发: $concurrency, 请求: $requests" | tee -a $RESULT_FILE
    
    # 运行压测
    echo "🚀 开始压测..." | tee -a $RESULT_FILE
    result=$(ab -n $requests -c $concurrency -T "application/json" -H "$TOKEN" -p $POST_DATA $URL 2>&1)
    ab_exit_code=$?
    
    # 检查ab命令执行结果
    if [ $ab_exit_code -ne 0 ]; then
        echo "❌ 压测执行失败 (退出码: $ab_exit_code)" | tee -a $RESULT_FILE
        echo "错误信息:" | tee -a $RESULT_FILE
        echo "$result" | grep -E "(apr_socket_recv|Connection refused|Connection timed out|failed|error)" | tee -a $RESULT_FILE
        return 1
    fi
    
    # 提取关键指标
    qps=$(echo "$result" | grep "Requests per second" | awk '{print $4}')
    avg_time=$(echo "$result" | grep "Time per request" | head -1 | awk '{print $4}')
    failed=$(echo "$result" | grep "Failed requests" | awk '{print $3}')
    p95_time=$(echo "$result" | grep "95%" | awk '{print $2}')
    connect_errors=$(echo "$result" | grep "Connect:" | awk '{print $2}')
    
    # 检查是否有严重错误
    if [ ! -z "$connect_errors" ] && [ "$connect_errors" -gt 0 ]; then
        echo "⚠️  连接错误: $connect_errors 个" | tee -a $RESULT_FILE
    fi
    
    if [ ! -z "$failed" ] && [ "$failed" -gt 0 ]; then
        failure_rate=$(echo "scale=2; $failed * 100 / $requests" | bc 2>/dev/null || echo "N/A")
        echo "⚠️  失败请求: $failed (${failure_rate}%)" | tee -a $RESULT_FILE
    fi
    
    # 记录结果
    if [ ! -z "$qps" ] && [ ! -z "$avg_time" ]; then
        echo "  ✅ QPS: $qps" | tee -a $RESULT_FILE
        echo "  ✅ 平均响应时间: ${avg_time}ms" | tee -a $RESULT_FILE
        echo "  📊 95%响应时间: ${p95_time}ms" | tee -a $RESULT_FILE
        echo "  📊 失败请求: $failed" | tee -a $RESULT_FILE
    else
        echo "❌ 无法解析测试结果" | tee -a $RESULT_FILE
        echo "完整输出:" | tee -a $RESULT_FILE
        echo "$result" | tee -a $RESULT_FILE
        return 1
    fi
    
    echo "  ---" | tee -a $RESULT_FILE
    return 0
}

# 创建测试数据
if [ ! -f "$POST_DATA" ]; then
    echo "创建测试数据文件..." | tee -a $RESULT_FILE
    echo '{"test":"data","timestamp":1234567890}' > $POST_DATA
fi



# 阶段1：低并发基准测试
echo "📊 阶段1：低并发基准测试 (总请求数: $TOTAL_REQUESTS)" | tee -a $RESULT_FILE

# run_test 1 $TOTAL_REQUESTS "单线程基准" || echo "⚠️ 单线程测试失败"
# sleep 3

run_test 10 $TOTAL_REQUESTS "低并发基准" || echo "⚠️ 低并发测试失败"
sleep 3
run_test 20 $TOTAL_REQUESTS "低并发基准" || echo "⚠️ 低并发测试失败"
sleep 3
run_test 50 $TOTAL_REQUESTS "低并发基准" || echo "⚠️ 低并发测试失败"
sleep 5

# 阶段2：中等并发测试（响应式开始显现优势）
echo "📊 阶段2：中等并发测试 (总请求数: $TOTAL_REQUESTS)" | tee -a $RESULT_FILE
# run_test 100 $TOTAL_REQUESTS "中等并发" || echo "⚠️ 中等并发测试失败"
# sleep 5
run_test 200 $TOTAL_REQUESTS "中等并发" || echo "⚠️ 中等并发测试失败"
sleep 5
run_test 300 $TOTAL_REQUESTS "中等并发" || echo "⚠️ 中等并发测试失败"
sleep 8

# 阶段3：高并发测试（响应式优势明显）  
echo "📊 阶段3：高并发测试 (总请求数: $TOTAL_REQUESTS)" | tee -a $RESULT_FILE
run_test 500 $TOTAL_REQUESTS "高并发" || echo "⚠️ 高并发测试失败，可能接近服务极限"
sleep 10
run_test 800 $TOTAL_REQUESTS "高并发" || echo "⚠️ 高并发测试失败，可能接近服务极限"
sleep 10
run_test 1000 $TOTAL_REQUESTS "高并发" || echo "⚠️ 高并发测试失败，可能接近服务极限"
sleep 10
run_test 1500 $TOTAL_REQUESTS "高并发" || echo "⚠️ 高并发测试失败，可能接近服务极限"
sleep 15

# 阶段4：极高并发测试（找到性能瓶颈）
echo "📊 阶段4：极高并发测试 (总请求数: $TOTAL_REQUESTS)" | tee -a $RESULT_FILE
echo "⚠️  开始极限测试，请监控服务状态..." | tee -a $RESULT_FILE
run_test 2000 $TOTAL_REQUESTS "极高并发" || echo "❌ 极高并发测试失败，已达到服务极限"
sleep 15
run_test 3000 $TOTAL_REQUESTS "极高并发" || echo "❌ 极高并发测试失败，已达到服务极限"
sleep 15
run_test 4000 $TOTAL_REQUESTS "极高并发" || echo "❌ 极高并发测试失败，已达到服务极限"




echo "测试完成！详细结果请查看: $RESULT_FILE" | tee -a $RESULT_FILE
echo "测试结束时间: $(date)" | tee -a $RESULT_FILE



## 压测结果

| 并发数 | QPS（同步） | 平均响应时间（同步） | 95%响应时间（同步） | QPS（响应式） | 平均响应时间（响应式） | 95%响应时间（响应式） |
| --- | --- | --- | --- | --- | --- | --- |
| 10 | 71.2 | 140.445 | 485 | 176.93 | 56.519 | 157 |
| 20 | 60.18 | 332.329 | 986 | 243.52 | 82.128 | 246 |
| 50 | 失败 | 失败 | 失败 | 252.08 | 198.353 | 508 |
| 100 | 72.43 | 1380.56 | 4332 | 240.29 | 416.168 | 814 |
| 200 | 74.3 | 2691.857 | 7655 | 309.46 | 646.285 | 1112 |
| 300 | 71.21 | 4212.883 | 10943 | 133.24 | 2251.537 | 1149 |
| 500 | 68.26 | 7324.498 | 17332 | 455.8 | 1096.98 | 1268 |
| 800 | 74.61 | 10722.655 | 24476 | 598.16 | 1337.427 | 2115 |
| 1000 | 73.73 | 13563.109 | 31676 | 682.45 | 1465.309 | 1649 |
| 2000 | 186.24 | 10739.042 | 29671 | 121.05 | 16522.11 | 4578 |
| 3000 | 失败(服务重启) | 失败(服务重启) | 失败(服务重启) | 1612.74 | 1860.185 | 4137 |
| 4000 | 失败(服务重启) | 失败(服务重启) | 失败(服务重启) | 1855.99 | 2155.183 | 2489 |


- 阻塞式代码在3000,4000并发都失败时网关服务重启了,和之前线上表现一致。
- 如果要体现响应式Redis的优势必须上大并发,如果并发过低,同步和异步的差异不明显甚至会出现异步性能低于同步的情况(这个是我遇到的实际情况,花了点时间查了资料)


false#### 图表说明：- X轴表示并发数
- 实线表示同步实现的性能数据
- 虚线表示响应式实现的性能数据
- 值为0的测试点表示测试失败
- QPS越高表示性能越好，响应时间越低表示性能越好


## 经验

响应式的优势在高并发、I/O密集型场景下才明显。如果并发不高,同步模式的线程池（通常几十到几百个线程）完全够用

false为什么低并发(例如10个)下响应式QPS更低：1. 响应式编程的开销：在低并发时，响应式框架的异步调度、事件循环、回调管理等开销比直接同步调用更大
2. 没有达到响应式的优势区间：响应式的优势在于高并发时不阻塞线程，但10个并发远未达到线程池瓶颈
响应式Redis的优势体现在：- 高并发场景（通常>100并发）：当同步方式的线程池耗尽时，响应式仍能保持性能
- I/O密集型操作：Redis操作本身就是I/O密集型，响应式在这种场景下优势明显
- 资源利用率：响应式使用更少的线程处理更多请求


## 参数调整

进一步优化Lettuce连接池,目的是看配置了连接池,网关表现情况

spring:
  redis:
    lettuce:
      pool:
        # 连接池中的最大空闲连接数。超过此数目的空闲连接将被释放
        max-idle: 200
        # 连接池中最小连接数，连接池中始终会保持至少这么多的空闲连接
        min-idle: 50
        # 连接池中的最大活动连接数。当连接数达到此值时，后续的连接请求将被阻塞
        max-active: 5000
        # 从连接池获取连接的最大等待时间，单位是毫秒
        max-wait: 1000

## 待办

  2 complete 备份master分支. branch: master_bak_日期