# 1.技术预研

[https://java2ai.com/docs/1.0.0.2/overview/?spm=4347728f.53938564.0.0.3be92b0e8GFDVx](https://java2ai.com/docs/1.0.0.2/overview/?spm=4347728f.53938564.0.0.3be92b0e8GFDVx)

|  |  |  |
| --- | --- | --- |
| 构建工作流、多智能体应用 | 使用 Spring AI Alibaba Graph |  |
| 自主规划能力的通用智能体 | 基于 Spring AI Alibaba 框架实现的 JManus 智能体 |  |


总的来说，根据不同场景，您可以选择使用 `ChatClient` 或 `Spring Ai Alibaba Graph` 两个核心组件来开发 AI 应用

|  |  |  |
| --- | --- | --- |
| ChatClient | 把 ChatClient 开发的 AI 应用叫做单智能体应用，这可能是我们最理想的智能体开发模式，它足够简单直接，即把所有的工具、上下文信息等给到模型，由模型持续决策、迭代直到最终完成任务解答。然而，事情远没有那么简单，模型的能力还远未达到我们想要的效果，当我们给模型的上下文、工具过多时，整体效果就会变差，有时事情的走向会严重偏离我们的预期。 |  |
| 工作流和多智能体 | 把复杂的问题拆解开来，用工作流和多智能体 |  |
|  |  |  |


|  |  |  |
| --- | --- | --- |
| 工作流（Workflow） | 工作流是以相对固化的模式来人为的拆解任务，将一个大的任务拆解为一个固化的有多个分支的流程。工作流的优势是确定性强，模型作为流程中的一个结点起到的更多是一个分类决策的职责，因此它更适合意图识别等类别属性强的应用场景。工作流也有明显的劣势，它要求开发人员对业务流程有深刻的理解，整个流程是由人绘制的，模型在其中更多的只是内容生成、总结、分类识别的作用，并不能最大化利用模型的推理能力，因此很多人诟病这种模式是不够智能的。- LlmNode（大模型节点） - QuestionClassifierNode（问题分类结点） - ToolNode（工具结点） - ...... | Spring AI Alibaba Graph |
| 多智能体（Multi-agent） | 复杂任务拆解的另一种解决方案是多智能体，相比于工作流，多智能体虽也遵循特定的流程，但是在整个决策、执行流程上具备更多的自主性和灵活性。多个子智能体间通过通信协作完成，最终完成任务解答 | Spring AI Alibaba Graph 可用来开发各种多智能体模式。官方社区目前发布了几款基于 Spring AI Alibaba Graph 开发的智能体产品，包括通用智能体 JManus、DeepResearch 智能体、AgentScope 等。 |


|  |  |  |
| --- | --- | --- |
| JManus 智能体平台 | 通用智能体自动规划、执行规划的能力 探索智能体在解决日常生活、工作效率等开放性问题方面的无限空间 | 企业级业务场景的典型特点是确定性，我们需要定制化的工具、子agent，需要稳定而又确定性强的规划与流程 |
| DeepResearch 智能体 | 帮助用户完成各类深度调研报告 |  |