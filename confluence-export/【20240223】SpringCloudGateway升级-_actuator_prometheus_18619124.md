# 【20240223】SpringCloudGateway升级-/actuator/prometheus

见:

目标: 访问/actuator/prometheus 可以检索到spring_cloud_gateway信息

api-gateway满足:

3.0.3版本

businesses-gateway、h3-gateway不是3.0.3的版本,虽然有gateway的指标,但是不是spring_cloud_开头的,如果需要统一,就需要升级

新分支:

```
feature/ORDER-66/prometheus-data 修改上报key解决
```

### businesses-gateway升级方式:

和api-gateway的pom依赖保持一致

升级过程中遇到的问题: logback中读取不到spring的配置

解决方法:

truespring:
  config:
    use-legacy-processing: true

再添加依赖:

true<dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>

配置:

本地是配置了allowCredentials: true， apollo上没有配置，如果apollo配置了true，则要修改allowedOrigins为allowedOriginPatterns
#allowedOriginPatterns: "*"
#allowedOrigins: "*"

[https://developer.aliyun.com/article/779195](https://developer.aliyun.com/article/779195)

--

dev环境:

[http://hydee-api-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus](http://hydee-api-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus)

[http://businesses-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus](http://businesses-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus)

[https://h3-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus](https://h3-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus) 虽然有gateway的指标,但是不是spring_cloud_开头的