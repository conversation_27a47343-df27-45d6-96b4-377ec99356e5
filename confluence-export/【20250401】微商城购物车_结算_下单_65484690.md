# 【20250401】微商城购物车/结算/下单

## 一、首页

### 获取购物车数量 /1.0/cart/_getCount

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "type": "CART",  "merCode": "500001" } | 实际接口中没有用到 |
| 返回体 | ``` {   "cartCount": 2,          // 购物车中商品总数量（含所有类型商品）   "expressCartCount": 1,   // 需快递配送的商品数量   "selfDeliveryCount": 1,  // 需自提的商品数量   "prescCount": 0          // 处方药商品数量（当前为0表示无处方药） } ``` |  |


### 商品加购 /1.0/cart

|  | 内容 | 备注 |  |
| --- | --- | --- | --- |
| 请求体 |  | 内容 | 备注 |
| 返回体 | 请求体 | {  "specStores": [ // 需要操作的规格-门店组合列表  {  "specId": "1779061616871294979", // [必填]规格ID（商品规格唯一标识）  "storeId": "WSC0000", // [必填]门店ID（商品所属门店）  "spCode": null // 供应商编码（云仓业务使用，常规商品为null）  }  ],  "choseFlag": 0, // [必填]操作类型（1:选中，0:取消选中）  "type": "CART", // 操作场景标识（CART表示购物车页操作）  "merCode": "500001" // [必填]商户编码（系统分账标识） } |  |
|  | 响应体 | 参考购物车详情界面 |  |


## 二、购物车界面

### 获取购物车详情 /1.0/cart/_get

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "type": "CART", // 请求类型（购物车相关）  "deliveryType": 2, // 配送方式 (1: 门店配送, 2: 快递配送)  "currentStoreId": "1736943708675211265", // 当前门店ID  "latitude": 30.574471, // 纬度（地理位置）  "longitude": 103.923767, // 经度（地理位置）  "merCode": "500001" // 商户编码（必填字段） } |  |
| 返回体 | ``` {   "code": "10000",                    // 响应码 (10000表示成功)   "msg": "操作成功",                  // 响应消息   "data": {                           // 响应数据主体     "storeCommodityList": [           // 门店商品列表       {         "merCode": "500001",          // 商户编码         "storeId": "WSC0000",         // 门店ID         "center": null,               // 是否中心店(1:是, 0:否)         "storeName": "旗舰店",        // 门店名称         "isValid": 1,                 // 是否有效(1:有效)         "commodities": [              // 商品明细列表           {             // ===== 商品基础信息 =====             "commodityId": "1785220417385229824",  // 商品ID             "specId": "1785220417385230080",       // 规格ID             "merCode": "500001",                   // 商户编码             "spCode": null,                        // 供应商编码             "storeId": "WSC0000",                  // 门店ID             "storeName": "旗舰店",                 // 门店名称             "count": 2,                            // 购买数量             "limitNum": 1000,                      // 限购数量             "status": 1,                           // 商品状态(1:有效)             "choseFlag": 1,                        // 是否选中(1:是,0:否)                          // ===== 商品展示信息 =====             "activityId": "",                      // 活动ID             "displayName": "人参健脾丸(水蜜丸)_腾药_4g*60袋 4G*60袋",  // 展示名称             "specName": "4G*60袋",                 // 规格名称             "price": 158.0,                        // 当前售价             "beforePrice": 158.0,                  // 原价/活动前价格             "erpCode": "143302",                   // ERP编码             "commodityName": "人参健脾丸(水蜜丸)_腾药_4g*60袋",  // 商品全名             "commodityType": 1,                    // 商品类型(1:普通商品)             "isVirtual": 0,                        // 是否虚拟商品(0:实物)             "drugType": 2,                         // 药品类型(2:乙类OTC)                          // ===== 商品图片 =====             "picUrl": "https://...98f2.png",       // 规格图片URL             "mainPic": "https://...fc0f.png",      // 商品主图URL                          // ===== 价格信息 =====             "mPrice": 0.0,                         // 标价(可能为会员价)             "assemblePrice": null,                 // 组合商品价格             "goodsSalesPrice": 158.0,              // 实际销售价             "couponSalesPrice": 108.29,            // 券后价             "totalCouponSalesPrice": 216.57,       // 总券后价(数量*券后价)                          // ===== 库存信息 =====             "stock": 0,                            // 当前库存             "sharedStock": 100,                    // 共享库存数量             "sharedStockDTO": {                    // 共享库存详情               "merCode": "500001",               "commodityId": "1785220417385229824",               "specId": "1785220417385230080",               "orgId": "4088373249611662137",     // 机构ID               "orgCode": "D900",                  // 机构编码               "stock": 100                        // 可用库存             },                          // ===== 商品属性 =====             "weight": null,                        // 商品重量(g)             "specSkuList": [                       // 规格SKU列表               {                 "skuKeyId": "1001",                // SKU属性ID                 "skuKeyName": "规格型号",          // SKU属性名                 "skuValue": "4G*60袋"              // SKU值               }             ],             "distributionDay": 7,                  // 配送时效(天)                          // ===== 促销活动信息 =====             "pmtProductType": "N",                 // 商品类型(N:正品)             "activityDiscountAmont": 0,            // 活动优惠金额             "commodityLevelActivities": [],        // 单品级活动列表             "orderLevelActivities": [],            // 订单级活动列表                          // ===== 特殊商品标识 =====             "epidemicRegistration": 0,             // 是否疫情管控商品             "needHealthCode": 0,                   // 是否需要健康码             "medicalInsuranceCode": null,          // 医保编码                          // ===== 渠道信息 =====             "deliveryChannelCode": "wechat-applet", // 投放渠道(微信小程序)             "centerStoreId": "1736943708675211265",// 旗舰店ID                          // ===== 价格标签 =====             "priceTag": 12,                        // 价格场景标识(12:券后价)                          // ===== 会员相关 =====             "paidMemberFlag": false,               // 是否付费会员             "hasCouponPrice": true,                // 是否有券后价             "isDiscountPrice": 0,                  // 是否专属折扣价             "isPromoteProducts": 0,                // 是否推广商品             "isMembershipPrice": 0,                // 是否心钻价             "vipDiscount": 0,                      // 会员优惠金额             "vipDiscountPlus": 0                   // 会员折上折优惠           },           // 第二个商品结构同上...         ],                  // ===== 门店级汇总信息 =====         "chooseCommodityCount": 2,     // 已选商品数量         "reducePrice": 100.0,          // 预估优惠总额         "totalPrice": 217.8,           // 商品总价(优惠后)         "beforePrice": 317.8,          // 优惠前总价         "centerStoreId": "1736943708675211265",  // 旗舰店ID         "isCurrentStore": 0,           // 是否当前门店(0:否)         "isDistribution": 0            // 是否支持配送(0:否)       }     ],          // ===== 全局汇总信息 =====     "chooseCommodityCount": 2,         // 全店已选商品总数     "reducePrice": 100.0,              // 全店预估优惠总额     "totalPrice": 217.8,               // 全店商品总价(优惠后)     "beforePrice": 317.8,              // 全店优惠前总价          // ===== 优惠详情 =====     "predThriftRespDTO": {             // 预计优惠详情       "couponThrift": 100.0,           // 优惠券节省金额       "vipThrift": 31.78,              // 会员价优惠       "totalThrift": 131.78,           // 累计节省金额       "paidMemberFlag": false          // 是否付费会员     }   },   "timestamp": 1743993388448           // 响应时间戳 } ``` |  |


### 是否显示领取优惠券标签,返回可领取优惠券门店id集合 /1.0/coupon/_couponsTag

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "stores": [ // 门店商品erp编码列表（必填字段）  {  "storeId": "WSC0000", // 门店ID  "erpCodes": [ // ERP编码集合  {  "productCode": "143302", // 商品ERP编码（对应商品唯一标识）  "productTypeCode": "bc1864fe863644f6ae2ab4b38d4f5cd6", // 商品分类编码  "applyPaidMemberPrice": false // 是否应用付费会员价  },  {  "productCode": "100059", // 第二个商品的ERP编码  "productTypeCode": "c9cda6d423db42fca31284f1b3a917c8", // 分类编码  "applyPaidMemberPrice": false   }  ]  }  ],  "merCode": "500001" // 商家编码（必填字段） } |  |
| 响应内容 | {  "code": "10000",  "msg": "操作成功",  "data": [  "1736943708675211265",  "WSC0000"  ],  "timestamp": 1743993388571 } |  |


### 优惠卷列表 /1.0/coupon/_search

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "stores": [ // 门店商品信息列表（用于商品级优惠券查询）  {  "storeId": "WSC0000", // 门店ID  "spCode": null, // 供应商编码（云仓业务使用）  "erpCodes": [ // 商品ERP编码集合  {  "productCode": "143302", // 商品ERP编码（必填）  "productTypeCode": "bc1864fe863644f6ae2ab4b38d4f5cd6" // 商品分类编码  },  {  "productCode": "100059", // 第二个商品ERP编码  "productTypeCode": "c9cda6d423db42fca31284f1b3a917c8" // 分类编码  }  ]  }  ],  "merCode": "500001" // 商家编码（必填字段） } |  |
| 响应体 | {  "code": "10000",  "msg": "操作成功",  "data": [  {  "id": 69409,  "idprefix": null,  "merCode": "500001",  "storeId": null,  "title": "满减1219",  "assistantTitle": null,  "money": 20.0,  "levelId": null,  "becomeEffectiveTime": null,  "loseEffectiveTime": null,  "limitCount": null,  "allCount": null,  "residueCount": 999999919,  "description": "",  "serviceTel": null,  "status": 1,  "remindTime": null,  "color": null,  "type": 2,  "useNotice": null,  "sendType": 2,  "moneyLimit": 0.0,  "isLimitgoods": null,  "isLimitstore": null,  "useType": null,  "conditionValue": null,  "isPublicSend": null,  "getStartTime": "2024-12-19 14:48:00",  "getEndTime": "9999-12-31 00:00:00",  "invalidDays": null,  "effectiveDays": null,  "imduseUrl": null,  "isDelete": null,  "offlineUseLimitOneself": null,  "receiveObject": null,  "limitGoods": null,  "limitStores": null,  "giftGoods": null,  "cost": null,  "idprefixCount": null,  "canTakeFlag": true,  "costType": null,  "activityId": 134560,  "sceneRule": 7,  "useRule": 0.0,  "denomination": 20.0,  "denominationType": 1,  "combineProductRule": 1,  "listCouponStoreEntity": [],  "listCouponProductEntity": [],  "listCouponProductTypeEntity": [],  "state": 1,  "activityState": 1,  "note": "",  "totalCount": 999999999,  "perCount": 10,  "perCountGet": 1,  "perCountGetAvailable": 1,  "totalReceiveCountActivity": 80,  "activityType": 1,  "shopRule": 1,  "productRule": 1,  "timeRule": 4,  "beginTime": "2024-12-19T14:48:00",  "endTime": "9999-12-31T00:00:00",  "integral": 0.0,  "amount": 0.0,  "createFrom": 1,  "pageStatus": null,  "usableReason": null,  "saleAmount": null,  "limitUsedNum": null,  "totalUsedNum": null,  "giftLimitNum": 0,  "deliveryType": "1,2,3",  "couponProductDetails": null,  "maxPrice": 0.0,  "useRuleType": 0,  "sceneRuleList": [  1,  2,  4  ],  "ctype": 2,  "cname": "满减1219"  }  ],  "timestamp": 1743994184348 } |  |


### 编辑商品数量 1.0/cart

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "choseFlag": 1, // [必选]是否选中(1:是 0:否)，默认值1  "commodityId": "1785220417385229824", // [必填]商品ID（NotBlank校验）  "drugType": 2, // [重要]药品类型(2:乙类OTC)  "limitNum": 1000, // 商品限购数量  "specId": "1785220417385230080", // [必填]规格ID（NotBlank校验）  "storeId": "WSC0000", // [重要]门店ID  "spCode": null, // 供应商编码（云仓业务使用，非云仓为null）  "storeName": "旗舰店", // 门店名称（展示用）  "count": 1, // [必填]购买数量（NotNull校验）  "refreshCart": true, // 是否刷新购物车（true触发重新计算）  "isCartOperate": 1, // 是否购物车操作(1:加购操作)  "deliveryChannelCode": "wechat-applet", // 投放渠道（微信小程序场景）  "merCode": "500001" // [必填]商户编码 } |  |
| 响应体 | 参考购物车详情界面 |  |


### 选择商品 /1.0/cart/_choose

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "specStores": [ // 需要操作的规格-门店组合列表  {  "specId": "1779061616871294979", // [必填]规格ID（商品规格唯一标识）  "storeId": "WSC0000", // [必填]门店ID（商品所属门店）  "spCode": null // 供应商编码（云仓业务使用，常规商品为null）  }  ],  "choseFlag": 0, // [必填]操作类型（1:选中，0:取消选中）  "type": "CART", // 操作场景标识（CART表示购物车页操作）  "merCode": "500001" // [必填]商户编码（系统分账标识） } |  |
| 响应体 | 参考购物车详情界面 |  |


### 删除购物车商品 /1.0/cart/_del 清空购物也调用此接口

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "type": "CART", // 操作场景标识（CART表示购物车页操作）  "specStores": [ // [必填]需要删除的商品规格-门店组合列表  {  "specId": "1779062851050070019", // [必填]规格ID（商品规格唯一标识）  "storeId": "10063032", // [必填]门店ID（商品所属门店）  "spCode": null, // 供应商编码（云仓业务使用，常规商品为null）  "delCount": 1 // 删除数量（可选，默认删除全部）  }  ],  "merCode": "500001" // [必填]商户编码（系统分账标识） } |  |
| 响应体 | 参考购物车详情界面 |  |


此接口调用后，又调用了 /1.0/cart/getCount 接口 

### 订单确认页面初始进入数据加载 /1.0/order/orderConfirmInit 类似于结算页初始化

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "orderType": "N", // [必填]订单类型(N:正常订单)  "specId": "", // 商品规格ID（立即购买场景使用）  "storeId": "WSC0000", // 门店ID（自提订单必填）  "spCode": "", // 供应商编码（云仓订单使用）  "isB2cOrder": "1", // 是否B2C订单(1:是)  "isPresaleOrder": 0, // 是否预售订单(0:否)  "multiCartCommodity": [], // [异常]应为购物车商品列表（正常应有数据）  "prescriptionApprovalAddDTO": null, // 处方单信息（处方药订单需要）  "isCloudGoods": 0, // 是否分销云仓商品(0:否)  "couponCode": null, // 指定优惠券编码  "merCode": "500001" // [必填]商户编码 } |  |
| 响应体 | {  "code": "10000", // 状态码  "msg": "操作成功", // 返回消息  "data": {  "totalOrderAmount": 18.5, // 订单商品总额  "couponDeduction": 14.8, // 优惠券优惠金额  "actualFreightAmount": 0.0, // 运费  "activityDiscountAmont": 3.7, // 活动优惠金额  "otherDiscountAmont": 0.0, // 其他优惠金额  "totalActualOrderAmount": 0.0, // 订单实际需支付的总金额  "allCoupon": [ // 普通优惠券列表(折扣/满减)  {  "id": "1798270832367009024", // 优惠券ID  "idprefix": "R911812803", // 优惠券前缀  "money": 100.0, // 优惠券金额  "becomeEffectiveTime": "2024-04-30 10:15:28", // 生效时间  "loseEffectiveTime": "9999-12-31 00:00:00", // 失效时间  "type": 2, // 优惠券类型  "denomination": 100.0, // 面额  "denominationType": 1, // 面额类型  "cname": "诺和测试-大额满减" // 优惠券名称  }  ],  "cashCoupons": [ // 可叠加使用的现金券  {  "id": "1798270680641207296", // 现金券ID  "idprefix": "C070694168", // 现金券前缀  "money": 100.0, // 现金券金额  "becomeEffectiveTime": "2024-05-06 11:03:25", // 生效时间  "loseEffectiveTime": "9999-12-31 00:00:00", // 失效时间  "type": 5, // 现金券类型  "denomination": 100.0, // 面额  "denominationType": 1, // 面额类型  "cname": "支付券-现金券-100", // 现金券名称  "pageStatus": 3, // 页面状态  "usableReason": "可用该券的商品实付金额未大于0元" // 可用原因  }  ],  "couponCodes": [ // 建议使用的优惠券编码  "R911812803"  ],  "couponInitReqDTO": { // 优惠券初始化请求DTO  "amount": 14.8, // 金额  "originAmount": 18.5, // 原始金额  "storeCode": "ALQ1", // 门店编码  "paymentList": [ // 支付列表  {  "productCode": "100688", // 商品编码  "payment": 14.8, // 支付金额  "originPayment": 18.5, // 原始支付金额  "typeCode": "bc1864fe863644f6ae2ab4b38d4f5cd6", // 类型编码  "gainType": 0, // 获取类型  "useCoupon": true, // 是否使用优惠券  "commodityOrigin": 1, // 商品来源  "collectionMark": false, // 收藏标记  "commodityCount": 1, // 商品数量  "applyPaidMemberPrice": false // 是否应用付费会员价  }  ],  "spCode": null // 供应商编码  },  "userAddress": { // 用户收货地址  "id": 1234191, // 地址ID  "cityId": 120100, // 城市ID  "address": "天津市宝坻区宝平街道办", // 详细地址  "receivingPreson": "测试", // 收货人  "receivingPresonTel": "18194263243", // 收货人电话  "isdefault": 1, // 是否默认地址  "longitude": "117.297641", // 经度  "latitude": "39.717823", // 纬度  "houseNumber": "19", // 门牌号  "cityName": "天津市", // 城市名称  "areaId": 120115, // 区域ID  "areaName": "宝坻区", // 区域名称  "provinceId": 120000, // 省份ID  "provinceName": "天津市", // 省份名称  "merCode": "500001", // 商户编码  "fullAddress": "天津市宝坻区开元路1号", // 完整地址  "fullDetailAddress": "天津市宝坻区开元路1号天津市宝坻区宝平街道办19" // 详细完整地址  },  "storeResDTO": { // 门店信息  "province": "云南省", // 省份  "city": "昆明市", // 城市  "area": "呈贡区", // 区域  "address": "中国(云南)自贸区昆明片区经开区洛羊街道大冲社区鸿翔路1号一心堂2号门一楼", // 详细地址  "mobile": "************", // 联系电话  "longitude": "102.848871", // 经度  "latitude": "24.904966", // 纬度  "stStatus": 1, // 门店状态  "openStatus": 1, // 营业状态  "isself": 1, // 是否自营  "isdelivery": 1, // 是否支持配送  "id": "1736943708675211265", // 门店ID  "merCode": "500001", // 商户编码  "stCode": "ALQ1", // 门店编码  "stName": "旗舰店", // 门店名称  "stFullName": "一心堂药业集团股份有限公司昆明鸿翔路连锁二店" // 门店全称  },  "prescriptionSheetMark": 0, // 处方药订单标识(0-非处方)  "orderType": "N", // 订单类型(N-正常订单)  "cartCommodityRespDTOS": [ // 商品列表  {  "commodityId": "1779063126938753795", // 商品ID  "specId": "1779063126938754051", // 规格ID  "merCode": "500001", // 商户编码  "storeName": "旗舰店", // 门店名称  "count": 1, // 数量  "displayName": "枯草杆菌二联活菌颗粒_妈咪爱_1g*10袋 1G*10袋", // 显示名称  "specName": "1G*10袋", // 规格名称  "price": 18.5, // 价格  "beforePrice": 18.5, // 原价  "erpCode": "100688", // ERP编码  "commodityName": "枯草杆菌二联活菌颗粒_妈咪爱_1g*10袋", // 商品名称  "commodityType": 1, // 商品类型  "picUrl": "[https://shop-cdn.yxtmart.cn/shop/101-100/100688/100688-1.jpg](https://shop-cdn.yxtmart.cn/shop/101-100/100688/100688-1.jpg)", // 图片URL  "activityDiscountAmont": 3.7 // 活动优惠金额  }  ],  "totalGoodsWeight": 22, // 商品总重量(g)  "deliveryList": [ // 配送方式列表  {  "deliveryType": 0, // 配送类型  "deliveryName": "普通快递", // 配送名称  "paySetList": [ // 支付方式列表  {  "payType": 0, // 支付类型  "payName": "在线支付" // 支付名称  }  ],  "actualFreightAmount": 0.0 // 实际运费  }  ],  "totalOrderAmountShow": 18.5, // 前端展示的订单总额  "balanceAmount": 0, // 可用余额  "setPayPassword": false, // 是否设置支付密码  "isSupportDeduction": false, // 是否支持心币抵邮费  "isConfigureAddress": true, // 是否配置收货地址  "priceSpreadForFreeFreight": 981.5, // 免运费差价  "epidemicRegistration": 0, // 疫情管控商品标识(0-否)  "needHealthCode": 0, // 需要校验健康码(0-否)  "isPresaleOrder": 0, // 是否预售订单(0-否)  "isCoverMedicalComm": false, // 是否包含医保商品  "isDisplayDiscountCode": 0, // 是否展示折扣码(0-否)  "vipDiscount": 0.0, // 心钻会员价优惠  "vipDiscountPlus": 0.0, // 心钻折上折优惠  "discountRate": 0, // 心钻折上折折扣} |  |


### 订单确认页面-待提交页面 /1.0/order/orderConfirm 类似于结算修改信息（收货地址、优惠券）

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "orderType": "N", // 订单类型(N-正常订单)  "storeId": "1736943708675211265", // 门店ID  "specId": "", // 商品规格ID  "spCode": "", // 供应商编码  "isB2cOrder": "1", // 是否B2C订单(1-是)  "isPresaleOrder": 0, // 是否预售订单(0-否)  "couponCodeList": [ // 使用的优惠券编码列表  "R911812803"  ],  "deliveryType": 0, // 配送类型(0-普通快递)  "userAddress": { // 收货地址信息  "id": 1234191, // 地址ID  "cityId": 120100, // 城市ID  "address": "天津市宝坻区宝平街道办", // 详细地址  "receivingPreson": "测试", // 收货人  "receivingPresonTel": "18194263243", // 收货人电话  "isdefault": 1, // 是否默认地址(1-是)  "longitude": "117.297641", // 经度  "latitude": "39.717823", // 纬度  "houseNumber": "19", // 门牌号  "cityName": "天津市", // 城市名称  "areaId": 120115, // 区域ID  "areaName": "宝坻区", // 区域名称  "provinceId": 120000, // 省份ID  "provinceName": "天津市", // 省份名称  "merCode": "500001", // 商户编码  "fullAddress": "天津市宝坻区开元路1号", // 完整地址  "fullDetailAddress": "天津市宝坻区开元路1号天津市宝坻区宝平街道办19" // 详细完整地址  },  "multiCartCommodity": [], // 批量商品列表  "usePayBalance": false, // 是否使用余额支付  "distributionFlag": true, // 是否分销标识  "isDeductionFreight": false, // 是否使用心币抵邮费  "prescriptionApprovalAddDTO": null, // 处方单审批信息  "orderScene": 2, // 订单场景(2-普通场景)  "isCloudGoods": 0, // 是否分销云仓商品(0-否)  "couponCode": null, // 指定使用的优惠券编码  "isDiscountPrice": false, // 推广商品是否覆盖折原价  "merCode": "500001" // 商家编码 } |  |
| 响应体 | {  "code": "10000", // 响应状态码(10000-成功)  "msg": "操作成功", // 响应消息  "data": { // 响应数据主体  "totalOrderAmount": 18.5, // 订单商品总额  "couponDeduction": 14.8, // 优惠券抵扣金额  "actualFreightAmount": 0.0, // 实际运费金额  "activityDiscountAmont": 3.7, // 活动优惠金额  "otherDiscountAmont": 0.0, // 其他优惠金额  "totalActualOrderAmount": 0.0, // 订单实际支付金额  "distributeDistance": null, // 配送距离(单位：公里)  "addGroupStock": null, // 拼团活动库存  "totalActualHb": null, // 订单支付心币数量  "deliveryTime": null, // 预计配送时间  "totalOrderAmountShow": 18.5, // 前端展示的订单总额  "payBalanceAmount": 0, // 余额支付金额  "deliveryList": [ // 配送方式列表  {  "deliveryType": 0, // 配送类型(0-普通快递)  "deliveryName": "普通快递", // 配送方式名称  "sort": 0, // 排序字段  "paySetList": [ // 支持的支付方式  {  "payType": 0, // 支付类型(0-在线支付)  "payName": "在线支付" // 支付方式名称  }  ],  "actualFreightAmount": 0.0, // 实际运费  "initialDeliveryPrice": null, // 初始运费  "inDistribute": 1, // 是否在配送范围内(1-是)  "deliveryErrorMsg": null, // 配送错误信息  "isOptional": 1, // 是否可选(1-是)  "deliveryTime": null, // 预计送达时间  "priceSpreadForFreeFreight": 981.5 // 免运费差价(还需多少金额免运费)  }  ],  "isSupportDeduction": false, // 是否支持心币抵邮费  "freightDeductionOfHb": null, // 邮费抵扣所需心币数  "priceSpreadForFreeFreight": 981.5, // 免运费差价  "cloudStoreCommodityList": null, // 云仓商品列表  "failCommodityList": null, // 购买失败的商品列表  "cartCommodityRespDTOS": [ // 购物车商品详情  {  "commodityId": "1779063126938753795", // 商品ID  "specId": "1779063126938754051", // 规格ID  "merCode": "500001", // 商家编码  "spCode": null, // 供应商 |  |


### 保存订单信息-提交订单 /1.0/order/addOrder

|  | 内容 | 备注 |
| --- | --- | --- |
| 请求体 | {  "activityDiscountAmont": 11.28, // 活动折扣金额  "specCount": 2, // 规格数量  "actualFreightAmount": 0, // 实际运费金额  "couponCodeList": [ // 想使用的优惠券编码列表  "R911812803"  ],  "couponDeduction": 45.12, // 优惠券抵扣金额  "deliveryType": 2, // 配送类型(0普通快递1配送上门2门店自提)  "isInvoice": 0, // 是否需要发票标识(1-需要 0-不需要)  "isPageOrder": 1, // 是否页面下单(0否1是)  "isCloudGoods": 0, // 是否云仓商品(0:否，1：是)  "isBaseWarehousePick": false, // 是否总部仓库自提  "orderRemark": "", // 订单备注  "orderType": "N", // 订单类型(N.正常订单 G.拼团订单 I.积分兑换订单 A.预约订单 F.分销订单 C.云仓订单)  "otherDiscountAmont": 0, // 其他折扣金额  "payMode": 0, // 支付类型(0, 在线支付,1货到付款)  "prescriptionApprovalAddDTO": null, // 处方单信息(如果是需求单则需要)  "medicalUser": null, // 处方订单-用药人信息  "epidemicRegistrationReqDTO": null, // 疫情管控信息  "epidemicRegistration": 0, // 疫情管控订单(0：否，1：是)  "sourceChannel": "wechat-applet", // 客户端渠道  "fromChannel": "wechat-applet", // 来源渠道  "sourceMedia": "", // 来源媒体(会员卡号，员工编码，门店编码)  "sourceChannelId": "", // 渠道ID(从直播页面立即购买的时候传)  "specId": "", // 商品规则ID  "deliveryChannelCode": "wechat-applet", // 投放渠道Code  "storeId": "10053987", // 门店ID  "spCode": null, // 供应商编码  "isB2cOrder": "", // 是否B2C订单  "isPresaleOrder": 0, // 是否预售订单(0:否，1：是)  "spreadStoreId": "10053987", // 推广门店ID  "isCartShopping": "1", // 是否购物车下单  "totalActualOrderAmount": 0, // 实际订单总金额  "totalOrderAmount": 56.4, // 订单总金额  "userAddress": {}, // 收货地址  "totalActualHb": null, // 订单支付心币数量  "multiCartCommodity": [], // 批量商品购买信息  "payBalanceAmount": 0, // 余额支付金额  "usePayBalance": false, // 使用余额支付  "payPassword": "", // 余额支付密码  "payAfterAudit": 1, // 处方单审方后支付(0：否，1：是)  "longitude": 103.923767, // 经度(用于计算当前用户定位与下单门店距离)  "latitude": 30.574471, // 纬度  "buyerUser": null, // 购买人用户信息  "thirdOrderExtendType": null, // 三方扩展订单类型(1 诺和，可扩展)  "novoOrderNo": null, // 诺和订单号  "discountCode": null, // 折扣码等于员工编码  "merCode": "500001" // 商家编码 } |  |
| 响应体 |  |  |