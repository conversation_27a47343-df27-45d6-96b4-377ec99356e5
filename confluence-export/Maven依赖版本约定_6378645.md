# Maven依赖版本约定

# 1. 介绍

本文档目的是约定Maven项目中使用的依赖版本管理规范,以确保生产环境的稳定性和可靠性。

# 2. 仓库地址

请将以下仓库地址添加到Maven项目的pom.xml文件中的<distributionManagement>部分：

javaEclipse<distributionManagement>
    <repository>
        <id>local-releases</id>
        <name>Nexus Release Repository</name>
        <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
    <snapshotRepository>
        <id>local-snapshots</id>
        <name>Nexus Snapshot Repository</name>
        <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </snapshotRepository>
</distributionManagement>

以上代码块指定了Maven的发布仓库和快照仓库的地址。

# 3. 版本约定

根据约定，我们将使用以下格式定义依赖版本：

- **线上版本：<version>1.0.0-RELEASE</version>**
- **测试版本：<version>1.0.0-SNAPSHOT</version>**


`其中，线上版本是指已经发布并且稳定可用的版本，而测试版本是指开发中或者带有最新功能和修复的版本，版本号1.0.0可变。`

# 4.使用约定

`为了确保生产环境的稳定性，我们不允许在生产环境中使用测试版本（SNAPSHOT版本）。请严格遵守一下规则：`

- 在生产环境中只能使用线上版本，即 `<version>1.0.0-RELEASE</version>`。
- 在开发和测试环境中可以使用测试版本，即 `<version>1.0.0-SNAPSHOT</version>`。


# 5. 上传说明

要上传Maven仓库并规定以来版本，你要按照一下步骤进行操作：

 1. 确保已经配置好Maven环境，并确保setting.xml文件中包含正确的仓库凭据信息。

 2. 打开终端或命令行界面，进入项目根目录。

 3. 运行以下命令将项目构建并上传到仓库：

javamvn clean deploy

 Maven将会执行项目的清理、编译、打包等操作，并将构建结果上传到仓库。确保在pom.xml文件中指定了正确的<distributionManagement>配置。

 4. 如果想上传SNAPSHOT版本的依赖，可以运行以下命令：

javamvn clean deploy -Dmaven.deploy.snapshot=true

 这会将构建结果上传到仓库的快照目录。

 5. 如果想上传一个特定的依赖，可以使用以下命令:

javamvn deploy:deploy-file -DgroupId=<groupId> -DartifactId=<artifactId> -Dversion=<version> -Dpackaging=<packaging> -Dfile=<file> -Durl=<url> -DrepositoryId=<repositoryId>

其中，`<groupId>` 是依赖的 Group ID，`<artifactId>` 是依赖的 Artifact ID，`<version>` 是依赖的版本号，`<packaging>` 是依赖的打包类型，`<file>` 是依赖的本地文件路径，`<url>` 是仓库地址，`<repositoryId>` 是仓库的 ID。

请注意，以上命令中的参数根据你的项目和需求进行调整。

# 6. 总结

通过遵循以上Maven依赖版本约定，我们可以有效管理和控制项目的依赖版本，确保生产环境的稳定性。请务必遵守该约定，并在开发和测试环境中使用测试版本，而在生产环境中使用线上版本。