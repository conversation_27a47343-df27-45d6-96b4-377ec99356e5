# 【20240325】rocketmq 停止消费


|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.653|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.659|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
get data from db:1793935118394767616
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.665|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.679|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.683|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.687|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.691|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
get data from db:1793935118394767616
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.708|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.716|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.720|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.723|Thread-38|INFO|c.h.m.b.o.s.rocket.RocketMQLifecycle:44|已停止消费:ClientConfig [namesrvAddr=**********:9876, clientIP=************, instanceName=9, clientCallbackExecutorThreads=4, pollNameServerInterval=30000, heartbeatBrokerInterval=30000, persistConsumerOffsetInterval=5000, unitMode=false, unitName=null, vipChannelEnabled=false, useTLS=false, language=JAVA, namespace=null]
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:08.739|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:09.386|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:09.397|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:10.381|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:10.390|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:11.381|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:11.394|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:12.377|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:12.381|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:12.484|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:12.496|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:13.480|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:13.489|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:14.477|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:14.485|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:640|Waiting for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.478|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:643|Successfully waited for workers to finish.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.496|org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1|INFO|o.s.s.c.ThreadPoolTaskScheduler:208|Shutting down ExecutorService
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.500|org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1|INFO|o.s.s.c.ThreadPoolTaskScheduler:208|Shutting down ExecutorService
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.501|org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1|INFO|o.s.s.c.ThreadPoolTaskScheduler:208|Shutting down ExecutorService
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.506|org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1|INFO|o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer:840|Consumer stopped
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.522|org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1|INFO|o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer:840|Consumer stopped
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.526|org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1|INFO|o.s.k.l.KafkaMessageListenerContainer$ListenerConsumer:840|Consumer stopped
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.556|Thread-38|INFO|o.s.s.c.ThreadPoolTaskScheduler:208|Shutting down ExecutorService 'taskScheduler'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.579|Thread-38|INFO|c.a.c.n.r.NacosServiceRegistry:74|De-registering from Nacos Server now...
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.587|Thread-38|INFO|c.a.c.n.r.NacosServiceRegistry:93|De-registration finished.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.595|xxl-job, executor TriggerCallbackThread|INFO|c.x.j.c.thread.TriggerCallbackThread:96|>>>>>>>>>>> xxl-job, executor callback thread destory.
2024-03-25T16:15:15.597687577+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.595|Thread-24|INFO|c.x.j.c.thread.TriggerCallbackThread:126|>>>>>>>>>>> xxl-job, executor retry callback thread destory.
2024-03-25T16:15:15.597690748+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.596|Thread-25|INFO|com.xxl.rpc.remoting.net.Server:70|>>>>>>>>>>> xxl-rpc remoting server stop.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.607|xxl-job, executor ExecutorRegistryThread|INFO|c.x.j.c.t.ExecutorRegistryThread:87|>>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registGroup='EXECUTOR', registryKey='hydee-business-order', registryValue='************:9999'}, registryResult:ReturnT [code=200, msg=null, content=null]
2024-03-25T16:15:15.608714157+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.608|xxl-job, executor ExecutorRegistryThread|INFO|c.x.j.c.t.ExecutorRegistryThread:105|>>>>>>>>>>> xxl-job, executor registry thread destory.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.608|Thread-38|INFO|com.xxl.rpc.remoting.net.Server:106|>>>>>>>>>>> xxl-rpc remoting server destroy success.
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.621|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'hdPosAccountingExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.622|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'riderPreCallExecutor'
2024-03-25T16:15:15.*********+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.622|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'longAsyncJobExecutor'
2024-03-25T16:15:15.*********+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.622|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'asyncCallExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.623|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'eventDataCleanExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.623|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'supplierStoreMessageExecutor'
2024-03-25T16:15:15.623923699+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.623|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'dorisPushDataTaskProducerExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.624|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'delayTaskProducerExecutor'
2024-03-25T16:15:15.624598282+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.624|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'push2WsMessageExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.624|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'orderPreStatisticsExecutor'
2024-03-25T16:15:15.625062771+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.624|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'exportTaskExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.625|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'thirdMessageLogExecutor'
2024-03-25T16:15:15.625661317+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.625|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'refundBillLogExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.625|Thread-38|INFO|o.s.s.c.ThreadPoolTaskExecutor:208|Shutting down ExecutorService 'billLogExecutor'
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.690|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
2024-03-25T16:15:15.692466566+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
2024-03-25T16:15:15.692954043+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
2024-03-25T16:15:15.*********+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.692|Thread-38|INFO|o.s.a.r.l.SimpleMessageListenerContainer:1231|Shutdown ignored - container is not active already
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.821|Thread-38|INFO|c.b.d.d.DynamicRoutingDataSource:182|closing dynamicDatasource  ing....
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.822|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2003|{dataSource-2} closing ...
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.875|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2075|{dataSource-2} closed
2024-03-25T16:15:15.*********+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.876|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2003|{dataSource-3} closing ...
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.895|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2075|{dataSource-3} closed
2024-03-25T16:15:15.*********+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.896|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2003|{dataSource-5} closing ...
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.906|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2075|{dataSource-5} closed
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.907|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2003|{dataSource-1} closing ...
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.913|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2075|{dataSource-1} closed
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.914|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2003|{dataSource-4} closing ...
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.917|Thread-38|INFO|c.alibaba.druid.pool.DruidDataSource:2075|{dataSource-4} closed
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.985|NettyClientSelector_1|INFO|RocketmqRemoting:95|closeChannel: close the connection to remote address[**********:40210] result: true
2024-03-25T16:15:15.987731579+08:00 |hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.986|NettyClientSelector_1|INFO|RocketmqRemoting:95|closeChannel: close the connection to remote address[**********:9876] result: true
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.990|NettyClientSelector_1|INFO|RocketmqRemoting:95|closeChannel: close the connection to remote address[**********:40110] result: true
|hydee-business-order|dev:************:12600|N/A|:||2024-03-25 16:15:15.992|Thread-38|INFO|o.a.coyote.http11.Http11NioPr