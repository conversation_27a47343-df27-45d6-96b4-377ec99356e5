# JDK8-JDK21 新特性汇总

## JDK8

### Lambda表达式

解释:

- Lambda表达式提供了一种更简洁的方式来表示匿名函数或匿名方法。它可以用于为函数式接口（只有一个抽象方法的接口）提供实现。在上面的例子中，Runnable是一个函数式接口，Lambda表达式() -> System.out.println("线程执行")替代了匿名内部类，使代码更加简洁易读。它减少了样板代码，提高了开发效率。


使用例子 ：

- 定义一个线程，在以前需要实现`Runnable`接口，代码如下：


javaThread thread = new Thread(new Runnable() {
    @OverJDK 8引入了新的日期和时间 API（java.time包），包括LocalDate（本地日期，不包含时间）、LocalTime（本地时间，不包含日期）、LocalDateTime（本地日期时间）等类。这些类是不可变的，线程安全的，克服了旧日期 API（如Date和Calendar）中存在的线程安全问题和易用性差的问题。它们提供了更直观的方法来处理日期和时间，如获取当前日期、比较日期等ride
    public void run() {
        System.out.println("线程执行");
    }
});
thread.start();

- 使用Lambda表达式后：


javaThread thread = new Thread(() -> System.out.println("线程执行"));
thread.start();

### 新的日期API

解释: 

- JDK 8引入了新的日期和时间 API（java.time包），包括LocalDate（本地日期，不包含时间）、LocalTime（本地时间，不包含日期）、LocalDateTime（本地日期时间）等类。这些类是不可变的，线程安全的，克服了旧日期 API（如Date和Calendar）中存在的线程安全问题和易用性差的问题。它们提供了更直观的方法来处理日期和时间，如获取当前日期、比较日期等


例子

- 使用`LocalDate`获取当前日期：


javaLocalDate now = LocalDate.now();
System.out.println("当前日期：" + now); // 输出类似：当前日期：2024 - 10 - 24

- 创建特定日期并比较大小：


javaLocalDate birthday = LocalDate.of(2024, 10, 1);
if (birthday.isBefore(now)) {
    System.out.println("生日已经过了");
}

### 引入Optional

解释: 

- Optional是一个容器对象，用于表示一个值存在或不存在。它可以有效避免空指针异常。以前在获取可能为null的值时，需要进行大量的null检查。而使用Optional后，可以通过其提供的方法（如ifPresent）来安全地处理值是否存在的情况，使代码更加清晰和健壮。


例子

- 定义一个可能返回null的方法，使用Optional包装返回值：


javapublic Optional<String> getUserName(int id) {
    // 模拟从数据库查询用户名，可能为null
    if (id == 1) {
        return Optional.of("Tom");
    } else {
        return Optional.empty();
    }
}

- 调用并处理Optional：


javaOptional<String> userName = getUserName(1);
userName.ifPresent(System.out::println); // 输出：Tom
Optional<String> emptyUserName = getUserName(2);
emptyUserName.ifPresent(System.out::println); // 无输出

### 使用Base64

解释: 

- JDK 8在java.util包下添加了Base64编码和解码的API。它可以将二进制数据编码为Base64格式的字符串，方便在文本环境中传输二进制数据，如在网络传输中。同时也可以对Base64编码的字符串进行解码，恢复成原始数据。


例子

- 字符串转Base64加密：


javaString str = "Hello,World";
byte[] encoded = Base64.getEncoder().encode(str.getBytes());
System.out.println("加密后：" + new String(encoded)); // 输出加密后的Base64字符串

- Base64解密：


javabyte[] decoded = Base64.getDecoder().decode(encoded);
System.out.println("解密后：" + new String(decoded)); // 输出：Hello,World

### 接口的默认方法和静态方法

解释: 

- 在JDK 8之前，接口只能包含抽象方法。现在接口可以包含默认方法（使用`default`修饰）和静态方法。默认方法允许接口在不破坏现有实现类的情况下添加新功能。实现类可以选择是否重写默认方法。静态方法可以直接通过接口名调用，它为接口提供了一些工具方法，增强了接口的功能。


例子

- 定义接口，包含默认方法和静态方法：


javapublic interface MyInterface {
    default void defaultMethod() {
        System.out.println("默认方法");
    }
    static void staticMethod() {
        System.out.println("静态方法");
    }
}

- 实现接口并调用方法：


javapublic class MyClass implements MyInterface {
    public static void main(String[] args) {
        MyClass myClass = new MyClass();
        myClass.defaultMethod();
        MyInterface.staticMethod();
    }
}

### 新增方法引用格式

解释: 

- 方法引用提供了一种更简洁的方式来引用方法。它包括静态方法引用（`类名::静态方法名`）、实例方法引用（`对象名::实例方法名`）、构造方法引用（`类名::new`）等。方法引用可以看作是Lambda表达式的另一种写法，当要传递给Lambda体的操作已经有实现的方法时，可以使用方法引用来代替Lambda表达式，使代码更加简洁。


例子

- 静态方法引用：


javaList<String> list = Arrays.asList("c", "b", "a");
list.sort(String::compareToIgnoreCase);
System.out.println(list); // 输出：[a, b, c]

- 构造方法引用：


javaList<String> list = Arrays.asList("Hello", "World");
List<String> stringList = list.stream().map(StringBuilder::new).collect(Collectors.toList());

### 新增Stream类

解释: 

- Stream API是用于处理数据流的。它可以对数据源（如集合、数组等）进行各种操作，包括筛选（filter）、映射（map）、排序（sorted）等中间操作，以及收集（collect）、遍历（forEach）等终端操作。它允许以声明式的方式处理数据，而不是像以前那样使用循环和条件语句。Stream可以实现顺序处理，也可以通过`parallel()`方法实现并行处理，提高数据处理的效率。


例子

- 使用Stream对集合进行过滤、映射和收集操作：


javaList<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6);
List<Integer> evenNumbersSquared = numbers.stream()
        .filter(n -> n % 2 == 0) // 过滤出偶数
        .map(n -> n * n) // 对偶数进行平方操作
        .collect(Collectors.toList()); // 收集结果
System.out.println(evenNumbersSquared); // 输出：[4, 16, 36]

### 注解相关的改变

解释: 

- JDK 8增强了注解的功能，允许在同一个声明上多次使用同一个注解，即支持重复注解。通过使用`@Repeatable`注解可以指定一个容器注解来存储重复的注解。这使得注解的使用更加灵活，例如在一个类上可以添加多个具有相同类型的注解来表示不同的信息。


例子

- 自定义重复注解


java@Repeatable(MyAnnotations.class)
@Retention(RetentionPolicy.RUNTIME)
@interface MyAnnotation {
    String value();
}
@Retention(RetentionPolicy.RUNTIME)
public @interface MyAnnotations {
    MyAnnotation[] value();
}

- 使用重复注解


java@MyAnnotation("第一个注解")
@MyAnnotation("第二个注解")
public class MyClass {
}

### 支持并行（parallel）数组

解释: 

- JDK 8引入了对并行数组操作的支持。`Arrays`类中的并行方法（如`parallelSort`、`parallelSetAll`等）可以利用多核处理器的优势，将数组的处理任务分解为多个子任务并行执行，从而提高处理效率。这些方法底层使用了Fork / Join框架来实现并行计算。


例子

- 对数组进行并行排序：


javaint[] array = new Random().ints().limit(1000000).toArray();
Arrays.parallelSort(array);

### 对并发类（Concurrency）的扩展

解释: 

- ```
JDK 8对并发类库进行了扩展，主要是在Fork / Join框架方面。`ForkJoinPool`是用于处理分治算法的线程池，它可以使任务被分解为多个子任务并行执行，然后再将结果合并。这使得在处理大规模数据或复杂计算任务时能够更高效地利用多核处理器的计算能力，提高程序的性能。
```


例子

- ```
使用`ForkJoinPool`进行并行计算
```


java  ForkJoinPool pool = new ForkJoinPool();
  int result = pool.invoke(new MyRecursiveTask()); 

- ```
自定义递归任务类
```


java       public class MyRecursiveTask extends RecursiveTask<Integer> {
           @Override
           protected Integer compute() {
               // 自定义并行计算逻辑
               return 0;
           }
       }

## JDK 9

### 模块化（Module System）

解释: 

- JDK9 最重要的特性之一，通过定义模块（module）来增强 Java 的封装性，将代码和数据分组，更好地管理依赖关系、封装内部实现等。


例子

java// 定义一个模块 mymodule
module mymodule {
  // 导出包 com.example.mymodule 使其对外可见
  exports com.example.mymodule;
  // 依赖另一个模块 anothermodule
  requires anothermodule;
}

### 提供了 of、copyOf、ofNullable 和 ofDimensions 等工厂方法（针对集合框架）

解释: 

- 这些工厂方法用于创建集合的不可变实例，如 `List`、`Set` 和 `Map`。


例子

java// 创建一个包含多个元素的不可变列表
List<String> list = List.of("Java", "Python", "C++");
// 创建一个包含可为空元素的不可变集合
Set<String> set = Set.ofNullable("Apple", "Banana", null);

### 接口支持私有方法

解释: 

- 允许在接口中定义私有方法，以支持接口中的方法实现代码复用。


例子

javapublic interface MyInterface {
  // 默认方法
  default void defaultMethod() {
    // 调用私有方法
    privateMethod();
  }
  // 私有方法
  private void privateMethod() {
    System.out.println("This is a private method in interface.");
  }
}

### Optional 类改进

解释: 

- 新增了 orElseThrow、ifPresentOrElse 等方法，提供了更灵活的处理方式。


例子

javaOptional<String> optional = Optional.of("Hello, JDK9!");
// 如果存在值就执行某个操作，否则执行另一个操作
optional.ifPresentOrElse(
  value -> System.out.println("Value is present: " + value),
  () -> System.out.println("Value is not present")
);

### 多版本兼容 Jar 包

解释: 

- 允许在同一个 Jar 包中包含多个版本的类，JVM 会根据运行时的版本要求加载相应的类版本。


例子

java// 假设有两个不同版本的类 ClassA 和 ClassB 在同一个 Jar 包中
// 编译时使用 --multi-release 选项来指定支持多版本
// 编译并打包后，JVM 会根据版本需求加载对应的类版本

### JShell 工具

例子

java// 启动 JShell 工具
jshell
// 在 JShell 中编写和运行代码
String str = "Hello, JShell!";
System.out.println(str);

### try-with-resources 的改进

解释: 

- 允许在 try-with-resources 语句中使用已存在的、符合自动关闭资源要求的对象，而不仅仅局限于在该语句中创建的资源。


例子

java// 创建一个实现 AutoCloseable 接口的对象
AutoCloseable resource = () -> System.out.println("Resource closed");
// 在 try-with-resources 语句中使用已存在的资源
try (resource) {
  System.out.println("Using resource...");
}

### Stream API 的改进

解释: 

- 增加了 `takeWhile`、`dropWhile`、`iterate` 等方法，提供了更灵活的流操作方式。


例子

java// 创建一个流
Stream<Integer> stream = Stream.of(1, 2, 3, 4, 5, 6);
// 使用 takeWhile 方法获取满足条件的前几个元素
List<Integer> result = stream.takeWhile(n -> n < 4).collect(Collectors.toList());

### 设置 G1 为 JVM 默认垃圾收集器

解释: 

- G1 垃圾收集器成为默认的垃圾收集器，提供了更好的性能和低延迟特性。


例子

java// 启动 Java 应用程序时，可以指定使用 G1 垃圾收集器（不过 JDK9 中已经是默认值）
java -XX:+UseG1GC -jar myapplication.jar

### 支持 http2.0 和 websocket 的 API

解释: 

- 提供了对 HTTP/2 和 WebSocket 的支持，增强了网络通信能力。


例子

java// 创建一个 HTTP/2 客户端
HttpClient client = HttpClient.newHttpClient();
HttpRequest request = HttpRequest.newBuilder()
  .uri(URI.create("https://example.com"))
  .build();
// 发送请求并获取响应
client.sendAsync(request, HttpResponse.BodyHandlers.ofString())
  .thenApply(HttpResponse::body)
  .thenAccept(System.out::println)
  .join();

## JDK 10

### 局部变量类型推断

解释: 

- 在 JDK10 之前，我们需要显式地声明局部变量的类型，如 `ArrayList<String> list = new ArrayList<String>()`。而使用局部变量类型推断后，可以用 `var` 关键字代替，编译器会根据变量的初始化表达式自动推断出变量的类型。这使得代码更加简洁，减少了冗余的类型声明，提高了开发效率，但 Java 依然是强类型语言，只是在语法上提供了更便捷的局部变量声明方式。


例子

javapublic class VarExample {
    public static void main(String[] args) {
        var list = new ArrayList<String>();
        list.add("Java");
        list.add("Python");
        for (var item : list) {
            System.out.println(item);
        }
        var map = new HashMap<String, Integer>();
        map.put("One", 1);
        map.put("Two", 2);
        for (var entry : map.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
    }
}

### 不可变集合的改进

解释: 

- JDK10 引入了新的工厂方法 `List.of()`、`Set.of()` 和 `Map.of()` 来创建不可变的集合实例。这些不可变集合在创建后不能被修改，如添加、删除或替换元素等操作都会导致 `UnsupportedOperationException` 异常。这有助于提高代码的安全性和可靠性，特别是在需要确保数据不可变的场景中，如多线程环境或 API 设计中。


例子

javapublic class ImmutableCollectionsExample {
    public static void main(String[] args) {
        // 创建不可变列表
        List<String> immutableList = List.of("Apple", "Banana", "Orange");
        // 创建不可变集合
        Set<String> immutableSet = Set.of("Red", "Green", "Blue");
        // 创建不可变映射
        Map<String, Integer> immutableMap = Map.of("One", 1, "Two", 2, "Three", 3);
        // 尝试修改不可变集合会抛出异常
        try {
            immutableList.add("Grapes");
        } catch (UnsupportedOperationException e) {
            System.out.println("不可变列表无法添加元素");
        }
    }
}

### 并行全垃圾回收器 G1

解释: 

- G1 垃圾回收器在 JDK10 中得到了改进，引入了并行的 Full GC 功能。在之前的版本中，G1 的 Full GC 是单线程的，可能会导致较长的停顿时间。而改进后的并行 Full GC 可以使用多个线程来执行垃圾回收任务，从而显著减少了 Full GC 的停顿时间，提高了应用程序的响应速度和吞吐量，适用于对延迟要求较高的应用场景。


例子

- **使用例子** ：在 `JVM` 启动参数中添加 `-XX:+UseParallelGC` 来启用并行 G1 垃圾回收器。


java// 启动应用程序并启用并行 G1 垃圾回收器
java -XX:+UseParallelGC -jar myapplication.jar

### 线程本地握手

解释: 

- 线程本地握手允许在不执行全局 VM 安全点的情况下执行线程回调，可以停止单个线程而不需要停止所有线程或等待所有线程都处于安全点。这使得一些原本需要在安全点才能执行的操作变得更加高效和灵活，减少了对整个应用程序性能的影响，提高了系统的并发性能和可维护性。


例子

- 线程本地握手主要用于在执行某些操作时，如获取线程快照、堆栈跟踪等，不需要停止所有线程或等待所有线程达到安全点。以下是获取当前线程快照的示例：


javapublic class ThreadLocalHandshakeExample {
    public static void main(String[] args) throws InterruptedException {
        ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
        // 获取当前线程的堆栈跟踪
        for (StackTraceElement element : Thread.currentThread().getStackTrace()) {
            System.out.println(element.getMethodName());
        }
        // 获取所有线程的线程快照
        ThreadInfo[] threadInfos = threadMXBean.dumpAllThreads(true, true);
        for (ThreadInfo threadInfo : threadInfos) {
            System.out.println("Thread Name: " + threadInfo.getThreadName());
        }
    }
}

### Optional 新增 orElseThrow() 方法

解释: 

- `Optional` 类在 JDK10 中新增了 `orElseThrow()` 方法，它提供了一种更简洁的方式来处理可选值为空的情况。当 `Optional` 对象中没有值时，调用 `orElseThrow()` 方法会抛出指定的异常，而不是像之前的 `get()` 方法那样抛出 `NoSuchElementException`。这使得代码更具可读性和可维护性，能够更灵活地处理可选值为空的场景。


例子

javapublic class OptionalOrElseThrowExample {
    public static void main(String[] args) {
        Optional<String> optional = Optional.ofNullable(null);
        try {
            String result = optional.orElseThrow(() -> new IllegalArgumentException("值不存在"));
            System.out.println(result);
        } catch (IllegalArgumentException e) {
            System.out.println(e.getMessage());
        }
    }
}

### Unicode 语言标签扩展

解释: 

- JDK10 对 Unicode 语言标签的支持进行了扩展，允许使用更丰富的语言标签来表示语言、地区、变体等信息。通过 `Locale` 类的构建器，可以更灵活地创建和操作语言标签，从而更好地支持国际化和本地化需求。


例子

javapublic class UnicodeLanguageTagExample {
    public static void main(String[] args) {
        Locale locale = new Locale.Builder().setLanguage("zh").setRegion("CN").setLanguageTag("t-zh-CN").build();
        System.out.println(locale.getLanguage()); // 输出：zh
        System.out.println(locale.getRegion()); // 输出：CN
        System.out.println(locale.getLanguageTag()); // 输出：t-zh-CN
    }
}

### 根证书

解释: 

- JDK10 引入了根证书功能，使得 `JVM` 可以更好地支持安全通信。通过自带的根证书库，可以自动验证服务器证书的有效性，避免了手动添加信任证书的繁琐操作，提高了应用程序的安全性和易用性。


例子

- 在 JDK10 及以后的版本中，`JVM` 自带了一个根证书库，用于验证服务器证书。在进行 HTTPS 请求时，会自动使用这些根证书进行验证。


javapublic class RootCertificatesExample {
    public static void main(String[] args) throws Exception {
        URL url = new URL("https://www.example.com");
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.connect();
        System.out.println("服务器证书验证成功");
    }
}

## JDK 11

### 字符串处理方法新增

解释: 

- 字符串新增操作函数


例子

- **isBlank,strip,lines**


javapublic class StringIsBlankExample {
    public static void main(String[] args) {
        String str1 = "";
        String str2 = "   ";
        String str3 = "Hello";
        System.out.println(str1.isBlank()); // 输出 true
        System.out.println(str2.isBlank()); // 输出 true
        System.out.println(str3.isBlank()); // 输出 false
    }
}

public class StringStripExample {
    public static void main(String[] args) {
        String str = "   Hello World   ";
        System.out.println("'" + str.strip() + "'"); // 输出 'Hello World'
    }
}

public class StringLinesExample {
    public static void main(String[] args) {
        String str = "Line1\nLine2\nLine3";
        str.lines().forEach(System.out::println);
        // 输出：
        // Line1
        // Line2
        // Line3
    }
}

### 用于 Lambda 参数的局部变量语法（var）

解释: 

- 可以使用 `var` 来声明 Lambda 表达式的参数类型。


例子

javavar consumer = (var s) -> System.out.println(s);  // 推断参数类型为String[2,5](@ref)
var sum = Stream.of(1, 2).reduce((var x, var y) -> x + y);  // 参数推断为Integer[1](@ref)

### HttpClient 重写

解释: 

- **支持 HTTP/1.1 和 HTTP/2** ：可以更好地处理现代 Web 协议。


例子

- 提供了更现代化的 API 来发送和接收 HTTP 请求，支持异步和同步操作


javaimport java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

public class HttpClientExample {
    public static void main(String[] args) throws Exception {
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://www.example.com"))
                .build();
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        System.out.println(response.body());
    }
}

- **支持 WebSockets** ：实现了 WebSocket 协议，可以进行双向通信。


javaimport java.net.URI;
import java.net.http.HttpClient;
import java.net.http.WebSocket;
import java.util.concurrent.CompletionStage;

public class WebSocketExample {
    public static void main(String[] args) {
        HttpClient client = HttpClient.newHttpClient();
        WebSocket webSocket = client.newWebSocketBuilder()
                .buildAsync(URI.create("ws://echo.websocket.org"), new WebSocket.Listener() {
                    @Override
                    public CompletionStage<?> onText(WebSocket webSocket, CharSequence data, boolean last) {
                        System.out.println("Received: " + data);
                        return CompletableFuture.completedFuture(null);
                    }
                }).join();
        webSocket.sendText("Hello, WebSocket!", true);
    }
}

### 可运行单一 Java 源码文件

解释: 

- 直接运行 `.java` 文件，无需显式编译。


例子

- ```
执行命令：`java SingleFileExample.java
```


javapublic class SingleFileExample {
    public static void main(String[] args) {
        System.out.println("Hello, JDK11!");
    }
}

### ZGC（可伸缩低延迟垃圾收集器）

解释: 

- ```
ZGC 通过使用分区和加载屏障等技术，在减少停顿时间的同时，还能有效地管理内存，适用于对延迟敏感的应用场景。
```


例子

- 配置 ZGC：`-XX:+UseZGC`


java//执行命令：`java -XX:+UseZGC ZGCExample`

### 支持 TLS 1.3 协议

解释: 

- 提供更安全、高效的加密通信。


例子

- TLS 1.3 协议在安全性、性能和隐私保护等方面都有显著提升，使用它可以更好地保护数据传输的安全。


javaimport javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.Socket;

public class TLS13Example {
    public static void main(String[] args) throws Exception {
        SSLContext sslContext = SSLContext.getDefault();
        SSLSocket socket = (SSLSocket) sslContext.getSocketFactory().createSocket("www.example.com", 443);
        socket.setEnabledProtocols(new String[]{"TLSv1.3"});
        BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        System.out.println(reader.readLine());
        socket.close();
    }
}

### Flight Recorder（飞行记录器）

解释: 

- 通过收集事件数据，可以帮助开发者深入了解 JVM 的运行状态，找出潜在的性能瓶颈和问题。


例子

- 记录 JVM 的运行时数据，用于性能分析和故障排查。


java-XX:StartFlightRecording=filename=recording.jfr

### Stream、Optional、集合 API 增强

解释: 

- **Stream API 增强** ：增加了 `takeWhile()` 和 `dropWhile()` 等方法,


例子

- `takeWhile()` 方法可以根据条件截取流中的元素，提高代码的可读性和简洁性。
- **Optional API 增强** ：增加了 `isEmpty()` 和 `orElseThrow()` 等方法。
- **集合 API 增强** ：增加了 `addAll()` 和 `removeAll()` 等方法。


javaimport java.util.stream.Stream;

public class StreamExample {
    public static void main(String[] args) {
        Stream<Integer> stream = Stream.of(1, 2, 3, 4, 5);
        stream.takeWhile(n -> n < 4)
                .forEach(System.out::println);
        // 输出：
        // 1
        // 2
        // 3
    }
}

import java.util.Optional;

public class OptionalExample {
    public static void main(String[] args) {
        Optional<String> optional = Optional.of("Hello");
        System.out.println(optional.isEmpty()); // 输出 false
        optional.orElseThrow(() -> new RuntimeException("Value not present"));
    }
}

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;

public class CollectionExample {
    public static void main(String[] args) {
        List<String> list1 = new ArrayList<>();
        list1.add("A");
        list1.add("B");
        List<String> list2 = new ArrayList<>();
        list2.add("C");
        list2.add("D");
        list1.addAll(list2);
        System.out.println(list1); // 输出 [A, B, C, D]
    }
}

## JDK12

### Switch 表达式扩展

解释: 

- 在 JDK 12 中，switch 表达式可以有返回值。通过使用箭头（->）可以为每个 case 分支指定返回值。还可以合并多个 case 分支，并且使用 yield 关键字来返回值，这种方式使代码更加简洁，避免了传统的 break 语句，在处理多个相关情况时更方便，同时也提升了代码的可读性。


例子

javapublic class SwitchExample {
    public static void main(String[] args) {
        int dayOfWeek = 3;
        String dayName = switch (dayOfWeek) {
            case 1 -> "Monday";
            case 2 -> "Tuesday";
            case 3, 4 -> { // 合并多个情况
                yield "Wednesday or Thursday"; // 使用 yield 返回值
            }
            case 5 -> "Friday";
            case 6, 7 -> "Weekend";
            default -> "Invalid day";
        };
        System.out.println(dayName);
    }
}

### 新增 NumberFormat 对复杂数字的格式化

解释: 

- NumberFormat 类的增强使得对复杂数字格式化更加方便。可以设置小数位数上限和是否使用分组（如千分位分隔符）。在处理财务数据、科学数据等需要特定格式的数字场景下非常有用，能够确保数字以合适且易读的格式展示。


例子

javaimport java.text.NumberFormat;

public class NumberFormatExample {
    public static void main(String[] args) {
        double number = 123456789.123456789;
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(3); // 设置小数位数
        numberFormat.setGroupingUsed(true); // 使用分组（如千分位分隔符）
        String formattedNumber = numberFormat.format(number);
        System.out.println(formattedNumber);
    }
}

### 字符串支持 transform、indent 操作

解释: 

- transform 方法允许对字符串进行自定义的转换操作，例如替换字符等。indent 方法可以对字符串进行缩进处理，这在生成格式化文本，如代码块、报告等需要特定缩进格式的场景下非常实用。


例子

javapublic class StringExample {
    public static void main(String[] args) {
        String originalString = "Hello, World!";
        // transform 操作
        String transformedString = originalString.transform(str -> str.replace('o', 'a'));
        System.out.println(transformedString); // Hell a, Warp!
        // indent 操作
        String indentedString = originalString.indent(4); // 向右缩进 4 个空格
        System.out.println(indentedString);
    }
}

### 新增方法（Path, Path）

解释: 

- 新增的 Path 相关方法使得文件路径操作更加方便。可以更灵活地处理文件和目录路径，例如在文件复制、移动等操作中，明确指定源路径和目标路径，避免了繁琐的路径拼接和转换，提高了文件操作的效率和代码的可维护性。


例子

javaimport java.nio.file.Path;
import java.nio.file.Paths;

public class PathExample {
    public static void main(String[] args) {
        Path sourcePath = Paths.get("C:/source/file.txt");
        Path targetPath = Paths.get("D:/target/file_copy.txt");
        // 假设这里使用文件操作的相关方法（如复制等）
        // Files.copy(sourcePath, targetPath); // 示例，需要导入 java.nio.file.Files
        System.out.println("Source path: " + sourcePath);
        System.out.println("Target path: " + targetPath);
    }
}

### Teeing Collector

解释: 

- Teeing Collector 可以收集多个结果。在这个例子中，它对一个整数流同时进行列表收集（Collectors.toList()）和求和（Collectors.summingInt(n->n)）操作。然后将这两个结果组合成一个包含列表和总和的映射。它在需要对流进行多种收集操作并且希望将这些结果整合在一起时非常有用，避免了多次遍历流来分别收集不同的结果。


例子

javaimport java.util.*;
import java.util.stream.Collectors;

public class TeelingCollectorExample {
    public static void main(String[] args) {
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
        Collector<Integer, ?, Map<String, Integer>> teeingCollector = Collectors.teeing(
            Collectors.toList(),
            Collectors.summingInt(n -> n),
            (list, sum) -> Map.of("list", list, "sum", sum)
        );
        Map<String, Integer> result = numbers.stream().collect(teeingCollector);
        System.out.println(result);
    }
}

### 支持 unicode 11

解释: 

-   - JDK 12 支持 Unicode 11，能够正确处理 Unicode 11 中的字符，包括新的表情符号等。这使得 Java 程序在处理各种语言的文本、特殊符号等时更加准确和全面，满足了全球化应用中对多样化字符的支持需求。
- JDK 12 支持 Unicode 11，能够正确处理 Unicode 11 中的字符，包括新的表情符号等。这使得 Java 程序在处理各种语言的文本、特殊符号等时更加准确和全面，满足了全球化应用中对多样化字符的支持需求。


例子

javapublic class UnicodeExample {
    public static void main(String[] args) {
        String emoji = "\ud83d\ude00"; // 表情符号，属于 Unicode 11 的字符
        System.out.println(emoji.codePointCount(0, emoji.length())); // 输出 1，正确识别表情符号为一个代码点
    }
}

### Shenandoah GC，新增的 GC 算法

解释: 

- Shenandoah 是一种低延迟的垃圾回收器。它通过将垃圾回收工作分散在应用程序线程运行期间，减少了垃圾回收过程中的停顿时间。对于对响应时间有严格要求的应用，如金融交易系统、实时数据处理系统等，Shenandoah GC 可以提供更好的性能表现，提高应用程序的吞吐量和用户体验。


例子

- 在启动 Java 程序时，可以通过指定 JVM 参数来使用 Shenandoah 垃圾回收器。


javajava -XX:+UseShenandoahGC -jar myapp.jar。

### G1 收集器的优化

解释: 

- G1 收集器的优化将 GC 的垃圾分为强制部分和可选部分。这种优化方式使得垃圾回收过程更加精细，提升了 GC 的效率。它可以根据应用程序的需求和内存使用情况，更合理地安排垃圾回收工作，减少了不必要的回收操作，从而提高了整个应用程序的性能。


例子

- 使用 G1 收集器时，JVM 会自动根据优化后的策略进行垃圾回收。例如 `java -XX:+UseG1GC -jar myapp.jar`。


## JDK13

### Switch 表达式扩展

解释: 

- `switch` 表达式允许更简洁的代码结构。
- `yield` 关键字用于返回结果，类似于 `return`。
- 如果没有返回结果，则使用 `break`。


例子

javaint bonus = switch (employeeGrade) {
    case "A" -> 10000;
    case "B" -> 5000;
    case "C" -> 2000;
    default -> {
        System.out.println("No bonus");
        yield 0;
    }
};

### 文本块

解释: 

- 文本块通过三个双引号 `"""` 表示。
- 它可以简化多行文本的处理，避免繁琐的转义字符。


例子

- 


javaString html = """
    <!DOCTYPE html>
    <html>
    <body>
    <h1>Hello, World!</h1>
    </body>
    </html>
    """;
System.out.println(html);

### SocketAPI 重构

解释: 

- 底层实现引入了 NIO，提高了性能。
- 对于频繁处理大量数据的服务器端程序，这有助于提高效率。


例子

javatry (Socket socket = new Socket("localhost ",8080)) {
    // 使用重构后的 Socket API 进行通信
} catch (IOException e) {
    e.printStackTrace();
}

### ZGC 优化

解释: 

- ZGC 优化释放未使用的内存，将长时间空闲的堆内存返还给操作系统。
- 如果配置了相同的堆最大和最小内存大小，则不会释放内存。


例子

java// 在启动 Java 程序时配置堆内存
java -XX:+UseZGC -Xmx4G -Xms4G MyApplication

## JDK 14

### instanceof 模式匹配

解释: 

- 在传统的 Java 版本中，我们需要先用`instanceof`判断对象类型，然后再进行类型转换，例如`if(obj instanceof String){ String str = (String)obj; ... }`，而 JDK14 的`instanceof`模式匹配直接将类型判断和赋值结合在一起，当`obj`是`String`类型时，就自动将其赋值给`str`变量，简化了代码，减少了出错的可能性，提高了代码的可读性和简洁性。


例子

javaObject obj = "Hello, JDK14!";
if (obj instanceof String str) {
    System.out.println(str.length());
}

### Record 类型

解释: 

- `Record`是一种新的类型，用于表示不可变的数据载体，类似于 Lombok 的`@Data`注解。它会自动为声明的字段生成构造函数、`getter`方法、`equals`方法、`hashCode`方法和`toString`方法等。在上述例子中，`Person`是一个`Record`类，它自动拥有`name`和`age`的构造函数参数、对应的`getter`方法以及正确的`equals`、`hashCode`和`toString`方法，大大减少了样板代码的编写。


例子

- 


javapublic record Person(String name, int age) {}
Person person = new Person("Tom", 20);
System.out.println(person.name()); // 输出：Tom
System.out.println(person.age()); // 输出：20
System.out.println(person); // 输出：Person[name=Tom, age=20]

### Switch 表达式 - 标准化

解释: 

- Switch 表达式在 JDK12 中引入为预览特性，JDK14 中对其进行了标准化。它允许将`switch`语句用作表达式，根据输入值返回相应的结果。与传统的`switch`语句相比，新的 Switch 表达式使用`->`替代了`break`，并且可以更简洁地处理多种情况，避免了常见的`break`遗漏等问题，增强了代码的可读性和可维护性。


例子

javaint dayOfWeek = 3;
String dayName = switch (dayOfWeek) {
    case 1 -> "Monday";
    case 2 -> "Tuesday";
    case 3 -> "Wednesday";
    case 4 -> "Thursday";
    case 5 -> "Friday";
    case 6 -> "Saturday";
    case 7 -> "Sunday";
    default -> "Invalid day";
};
System.out.println(dayName); // 输出：Wednesday

### 改进 NullPointerExceptions 提示信息

解释: 

- 在 JDK14 及之后的版本中，空指针异常的提示信息会更具体，能够准确指出是哪一个方法调用抛出了异常，例如会明确指出是`trim()`方法还是`length()`方法调用时发生了空指针异常，从而帮助开发者更快速地定位问题所在，降低了调试的难度。


例子

- 在之前的 Java 版本中，如果一行代码中有多个方法调用，当发生空指针异常时，很难确定到底是哪个方法调用导致了异常。


javaString str = null;
int length = str.trim().length(); // 可能抛出空指针异常

### 删除 CMS 垃圾回收器

解释: CMS（Concurrent Mark-Sweep）垃圾回收器在 JDK14 中被正式删除。这是因为 CMS 回收器存在一些局限性，例如在低延迟场景下表现不稳定，且随着 Java 技术的发展，出现了其他更优秀的垃圾回收器（如 G1、ZGC 等），这些垃圾回收器在不同场景下都能提供更好的性能和稳定性。删除 CMS 垃圾回收器有助于简化 JVM 的垃圾回收器体系，使开发和维护更加聚焦于更高效、更可靠的垃圾回收解决方案

## JDK 15

### EdDSA 数字签名算法

解释: 

- 引入基于 Edwards-Curve 的加密签名，提供更高的安全性和性能，兼容 OpenSSL 等库


例子

- EdDSA 支持 RFC 8032 标准，适合高安全场景（如区块链或 HTTPS），性能优于传统 ECDSA


java// 密钥生成与签名示例
KeyPairGenerator kpg = KeyPairGenerator.getInstance("Ed25519");
KeyPair kp = kpg.generateKeyPair();
Signature sig = Signature.getInstance("Ed25519");
sig.initSign(kp.getPrivate());
sig.update(msg);
byte[] signature = sig.sign();

// 通过 KeyFactory 构造公钥
KeyFactory kf = KeyFactory.getInstance("EdDSA");
EdECPublicKeySpec pubSpec = new EdECPublicKeySpec(paramSpec, new EdPoint(xOdd, y));
PublicKey pubKey = kf.generatePublic(pubSpec);

### Sealed Classes（封闭类，预览特性）

解释: 

- 通过 `sealed` 和 `permits` 关键字限制继承关系，增强代码安全性


例子

- `sealed` 声明封闭类，`permits` 指定允许的子类列表。
- 子类需用 `final`、`sealed` 或 `non-sealed` 修饰，防止滥用继承
- 需编译时加 `--enable-preview` 参数启用预览功能


javapublic abstract sealed class Shape permits Circle, Square {
    // 抽象类定义
}

public final class Circle extends Shape { /* 仅允许此类继承 */ }
public non-sealed class Square extends Shape { /* 允许进一步扩展 */ }

### Hidden Classes（隐藏类）

解释: 

- 动态生成仅供框架内部使用的类，减少内存占用且无法通过常规反射访问


例子

- 适用于运行时生成临时类（如动态代理、ORM 框架），避免污染类加载器。
- 通过 `MethodHandles` API 创建，生命周期与框架绑定。


javaMethodHandles.Lookup lookup = MethodHandles.lookup();
byte[] classBytes = /* 动态生成的字节码 */;
Class<?> hiddenClass = lookup.defineHiddenClass(classBytes, true).lookupClass();

### 移除 Nashorn JavaScript 引擎

解释: 

- Nashorn 自 JDK8 引入，但 ECMAScript 标准迭代快，维护成本过高


例子

- 移除 `jdk.scripting.nashorn` 模块及 `jjs` 工具。
- 替代方案：使用 GraalVM 的 JavaScript 引擎或第三方库（如 Rhino）


### DatagramSocket API 改进

解释: 

- 重构底层实现，提升虚拟线程（Loom 项目）兼容性和维护性


例子

- 新实现简化了多播和 IPv6 支持，减少历史遗留问题


java// 新实现无需修改代码，但性能更优
DatagramSocket socket = new DatagramSocket();
socket.send(new DatagramPacket(data, data.length, address, port));

### 文本块（Text Blocks）正式转正

解释: 

- 简化多行字符串（如 JSON、HTML）的书写


例子

- 自动处理缩进和换行，支持 `\` 合并行和 `\s` 保留空格


java// 旧写法
String html = "<html>\n" + "  <body></body>\n" + "</html>";

// 新写法（JDK15+）
String html = """
    <html>
        <body></body>
    </html>
    """;

### 模式匹配 instanceof（第二次预览）

解释: 

- 简化类型检查和类型转换


例子

- 减少冗余代码，支持条件组合判断


java// 旧写法
if (obj instanceof String) {
    String s = (String) obj;
    System.out.println(s.length());
}

// 新写法（直接绑定变量）
if (obj instanceof String s && s.length() > 5) {
    System.out.println(s);
}

### Records 类（第二次预览）

解释: 

- 声明不可变数据类，自动生成 `equals()`、`hashCode()` 等方法


例子

- DTO、配置类等纯数据传输场景


java// 传统 POJO 类（约 20 行代码）简化为一行
public record Point(int x, int y) { }

// 使用示例
Point p = new Point(10, 20);
System.out.println(p.x());  // 自动生成 getter

### ZGC 垃圾回收器转正

解释: 

- 从实验特性转为正式功能，支持 16TB 堆内存和亚毫秒级停顿


例子

java-XX:+UseZGC

## JDK 16

### 允许在 JDK C++ 源代码中使用 C++14 功能

解释: 

- 在 JDK 的开发过程中，其底层部分是用 C++ 编写的。以前受限于各种因素，开发人员在编写这些 C++ 代码时，不能使用 C++14 中的一些新特性。而 JDK16 中放宽了这一限制，允许开发人员使用 C++14 的新功能来编写 JDK 的底层代码，这可以让开发人员更高效地利用 C++14 提供的各种增强特性，如新的数据类型、函数特性等来优化 JDK 的底层实现，提升性能和代码的可读性。


例子

java如果之前在 JDK 某些底层 C++ 模块中需要用到 C++14 中的 std::make_unique 来更简洁地创建对象，那在 JDK16 及之后的版本中就可以直接使用该特性来重写相关代码了。

### ZGC 性能优化，去掉 ZGC 线程堆栈处理从安全点到并发阶段

解释: 

- ZGC 是一种低延迟的垃圾回收器，在之前的版本中，其对线程堆栈的处理需要在安全点（即 JVM 线程暂停的特定点）来进行，这会带来一定的停顿时间，影响性能。在 JDK16 中，对其进行了优化，将这部分处理从安全点移到并发阶段，也就是在 JVM 线程正常运行的时候就能处理，这样大大减少了 GC 停顿时间，提高了 ZGC 的性能，使其更适用于对延迟要求极高的应用场景。


例子

- 当部署了一个对响应时间极为敏感的大型金融交易系统，使用了 ZGC 作为垃圾回收器后，因为这一优化，在处理大量高频交易时，系统停顿时间明显减少，交易的响应速度更快，交易的成功率和系统的吞吐量都得到了提升。


### 增加 Unix 域套接字通道

解释: 

- Unix 域套接字是一种在 Unix 系统中用于进程间通信的机制，它比传统的网络套接字在本地通信时具有更高的效率和安全性。JDK16 增加了对 Unix 域套接字通道的支持，使得 Java 程序能够更高效地在本地 Unix 系统的进程之间进行通信，这对于一些需要大量本地进程间交互的应用来说，可以提升通信效率，减少资源消耗。


例子

java比如在一个基于微服务架构的系统中，多个微服务运行在同一台 Unix 服务器上，通过使用 Unix 域套接字通道，这些微服务之间可以快速、高效地传递消息和数据，相比之前使用网络套接字的方式，通信延迟更低，数据传输更稳定可靠。

### 弹性元空间能力

解释: 

- 元空间是用来存储类的元数据（如类的定义、方法、字段等信息）的内存区域。在之前的 JDK 版本中，元空间的大小和管理方式相对固定，当元数据过多时可能会导致元空间不足等问题。JDK16 引入的弹性元空间能力，使得 JVM 能够更灵活地根据应用的实际需求动态调整元空间的大小，自动释放不再使用的元空间，减少元空间相关的内存溢出风险，提高内存的使用效率。


例子

java对于一个需要动态加载大量类（如某些插件化架构的应用）的系统，弹性元空间能够随着类的不断加载和卸载，自动合理地分配和回收元空间，避免因元空间不足而导致应用崩溃或频繁的 GC 停顿，保证应用的稳定运行。

### 提供用于打包独立 Java 应用程序的 jpackage 工具

解释: 

- jpackage 是一个命令行工具，它允许开发者将 Java 应用程序打包成独立的、可安装的软件包，这些软件包可以运行在目标系统的原生环境中，无需用户单独安装 JDK。这大大简化了 Java 应用的部署和分发过程，使 Java 应用能够更方便地被非技术用户使用，也便于在各种不同的操作系统环境中进行快速部署。


例子

java开发了一个企业级的桌面办公软件，使用 jpackage 工具可以将其打包成适用于 Windows、macOS 等不同平台的安装包，用户只需双击安装包即可完成安装并直接运行该 Java 软件，就像运行普通的原生软件一样，无需关心 JDK 的安装和配置。

### 将 JDK14、JDK15 的一些特性正式引入，如 instanceof 模式匹配、record 的引入等最终到 JDK16 变成了 final 版本

解释: 

- instanceof 模式匹配和 record 是 Java 语言层面的重要特性。instanceof 模式匹配简化了对对象类型的检查和转换操作，在之前的版本是预览状态，JDK16 将其正式引入并确定为最终版本。record 是一种新的数据类型，用于表示不可变的数据聚合体，能够减少样板代码，提高代码的可读性和开发效率，同样在 JDK16 中成为正式稳定版本。


例子

- 对于 instanceof 模式匹配，当需要判断一个对象是否是某个特定类型并且对其进行操作时，可以更简洁地写为：


javaif (obj instanceof String s) {
    System.out.println(s.length());
}



- 对于 record，可以定义一个简单的记录类型来表示二维坐标点：


javapublic record Point(int x, int y) {} 

## JDK 17

### Free Java License（免费许可证）

解释: 

- Oracle 在 JDK 17 中推出 **Oracle No-Fee Terms and Conditions (NFTC)** 许可证,允许开发者免费将 Oracle JDK 用于商业和生产环境，并支持季度安全更新。唯一限制是禁止付费再分发。这一变化极大降低了企业成本，同时推动了 JDK 17 的普及。


例子

- 无需代码示例，开发者可直接从 Oracle 官网下载 JDK 17 并用于生产环境，无需额外付费或申请商业许可。


### 长期支持（LTS）与 Spring 生态支持

解释: 

- JDK 17 是继 JDK 11 后的下一代 LTS 版本，提供至少 8 年支持。Spring 官方宣布 **Spring Framework 6** 和 **Spring Boot 3** 将基于 JDK 17 构建表明其已成为企业级开发的新基准。


### 密封类（Sealed Classes）

解释: 

- 密封类通过 `sealed` 和 `permits` 关键字限制继承关系，增强类型安全性和代码可控性


例子

- 避免意外扩展，适用于状态机、图形库等场景


java// 定义密封接口
public sealed interface Animal permits Dog, Cat, Bird {
    void sound();
}

// 子类必须显式声明 final 或 sealed
public final class Dog implements Animal {
    @Override
    public void sound() { System.out.println("Woof!"); }
}

public final class Cat implements Animal {
    @Override
    public void sound() { System.out.println("Meow!"); }
}

### 统一日志异步刷新

解释: 

- JDK 17 引入异步日志刷新机制，通过缓存提升性能，减少 I/O 阻塞


### 移除实验性 AOT/JIT 编译器

解释: 

- JDK 17 移除了 GraalVM 的 AOT（提前编译）和 JIT（即时编译）实验性功能,因成熟度不足且维护成本高。开发者需转用稳定版 GraalVM 或 OpenJDK 的其他优化。


### 严格浮点模式（Always-Strict）

解释: 

- 恢复浮点运算始终遵循 IEEE 754 标准，消除不同平台间的计算差异


例子

javapublic strictfp class StrictFPExample {
    public static void main(String[] args) {
        double a = 0.1;
        double b = 0.2;
        System.out.println(a + b); // 始终输出 0.30000000000000004
    }
}

## JDK 18

### 指定 UTF - 8 作为标准 Java API 的默认字符集

解释: 

- 此更改使 Java 程序在处理文本数据时，字符集行为更加一致，减少了因字符集不统一导致的潜在问题，提高了代码的可移植性和可靠性，开发人员无需再担心因运行环境不同而导致的字符集相关错误。
- 如果系统默认字符集不是 UTF - 8，可能会导致乱码。而在 JDK18 中，无需额外设置，即可按照 UTF - 8 编码正确读取文件。


例子

- 在之前版本中，若未指定字符集，可能会因系统默认字符集不同出现问题。例如读取文本文件：


javaBufferedReader reader = new BufferedReader(new FileReader("file.txt"));
String line;
while ((line = reader.readLine()) != null) {
    System.out.println(line);
}
reader.close();

### 引入一个简单的 Web 服务器

解释: 

- 这个简单的 Web 服务器为开发者提供了快速搭建测试环境的能力，方便在开发过程中进行原型设计、临时编码和测试，尤其适合教学场景，降低了学习和开发的门槛，无需额外安装复杂的服务器软件。


例子

- 在命令行中，使用 `jwebserver` 命令启动一个 Web 服务器，在指定目录下放入一个 `index.html` 文件：


### 支持在 Java API 文档中加入代码片段

解释:

- 这使得 API 文档更加丰富和实用，开发人员可以直接在文档中查看相关的代码示例，更好地理解如何使用 API，减少了查找示例代码的时间，提高了文档的可读性和实用性，有助于知识的快速传播和代码的正确使用。


例子

- 在 Java 源代码的注释中使用 `@snippet` 标记：


java/**
 * {@snippet :
 * public void exampleMethod() {
 *     System.out.println("This is an example.");
 * }
 * }
 */
public class ExampleClass {
    // ...
}

### switch 模式匹配表达式

解释: 

- 通过使用 `switch` 表达式和模式匹配，可以根据对象的类型进行不同的处理，使代码更加简洁、清晰和易于维护，增强了 Java 编程语言的表达能力，能够更方便地处理复杂的面向数据的查询和逻辑分支。


例子

- 


javaObject obj = "Hello";
String result = switch (obj) {
    case String s -> "String: " + s.length();
    case Integer i -> "Integer: " + i;
    default -> "Other";
};
System.out.println(result);  // 输出：String: 5

### 弃用 Finalization 功能

解释: 

- Finalization 功能存在诸多缺陷，如延迟不可预测、行为不受约束等，可能导致资源泄漏或其他问题。弃用该功能后，鼓励开发人员采用更安全、可靠和可维护的资源管理方式，如 try - with - resources 语句和清洁器，有助于提高程序的稳定性和性能，减少潜在的资源管理问题。


例子

- 以前可能使用 finalize() 方法来释放资源：


javapublic class OldClass {
    @Override
    protected void finalize() throws Throwable {
        // 释放资源
        super.finalize();
    }
}

- 在 JDK18 中，建议使用 try - with - resources 语句来管理资源：


javatry (Resource resource = new Resource()) {
    // 使用资源
}

### 外部函数和内存 API（第二孵化器）

解释: 

- 该 API 允许 Java 程序直接调用外部函数（如本机库中的函数）和访问外部内存，无需使用 JNI（Java 原生接口），避免了 JNI 的复杂性和潜在的危险，提高了 Java 程序与外部系统交互的效率和安全性，为开发高性能、底层相关的 Java 应用提供了新的可能性。


例子

- 以下是一个简单的代码片段，展示如何使用外部函数和内存 API 访问外部库中的函数：


javaMemorySegment functionSymbol = ... ; // 获取外部函数符号
VarHandle varHandle = ... ; // 获取 VarHandle 用于调用函数
int result = (int) varHandle.invokeExact(functionSymbol, arg1, arg2);

## JDK 19

### 结构化并发

例子

- 在结构化并发模型下，多个任务（`compute1` 和 `compute2`）被包含在一个结构化任务作用域中。使用虚拟线程执行服务端应用的每个请求，每个请求都是一个结构化并发任务，能更简单地处理多个任务，且便于错误处理、提高可靠性和可观察性。


javaimport java.util.concurrent.*;

public class StructuredConcurrencyExample {
    public static void main(String[] args) {
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            executor.execute(() -> {
                try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
                    Future<Integer> result1 = scope.fork(() -> compute1());
                    Future<Integer> result2 = scope.fork(() -> compute2());
                    Integer sum;
                    try {
                        sum = result1.get() + result2.get();
                    } catch (InterruptedException | ExecutionException e) {
                        throw new RuntimeException(e);
                    }
                    System.out.println("Sum: " + sum);
                }
            });
        }
    }

    private static Integer compute1() {
        // 模拟耗时计算
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return 10;
    }

    private static Integer compute2() {
        // 模拟耗时计算
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return 20;
    }
}

### 记录模式

解释: 

- 通过记录模式可以很方便地对 `Person` 记录的组件进行解构，直接在 `instanceof` 表达式中获取记录的值，而无需调用访问方法，使代码更加简洁、易读。


例子

javapublic record Person(String name, int age) {}

public class RecordPatternExample {
    public static void main(String[] args) {
        Person person = new Person("John", 30);
        if (person instanceof Person(String name, int age)) {
            System.out.println("Name: " + name + ", Age: " + age);
        }
    }
}

### 外部函数和内存 API

解释: 

- 利用外部函数和内存 API，能够直接调用 C 语言库中的函数 `strlen` 来计算字符串长度。通过这种方式，Java 程序可以方便地与外部代码和数据进行互操作，避免了使用 Java 本地接口（JNI）可能带来的问题。


例子

- 


javaimport jdk.incubator.foreign.*;

public class ForeignFunctionExample {
    public static void main(String[] args) throws Throwable {
        try (ResourceScope scope = ResourceScope.newConfinedScope()) {
            // 获取 C 语言库中的函数
            VarHandle strlenHandle = CLinker.getInstance().downcallHandle(
                CLinker.systemLookup().lookup("strlen").get(),
                FunctionDescriptor.of(CLinker.C_LONG, CLinker.C_POINTER)
            );
            // 准备输入字符串
            MemorySegment str = MemorySegment.ofArray("Hello, World!".getBytes(StandardCharsets.UTF_8));
            // 调用外部函数
            long length = (long) strlenHandle.invokeExact(str);
            System.out.println("String length: " + length);
        }
    }
}

### 虚拟线程

解释: 

- 创建了大量虚拟线程来执行任务，虚拟线程的开销远低于传统线程，可以轻松创建数千甚至数百万个虚拟线程，大大减少了编写、维护和观察高吞吐量并发应用的工作量，提高了服务器应用程序的扩展性。


例子

javapublic class VirtualThreadExample {
    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 1000; i++) {
            new Thread(() -> {
                try {
                    // 模拟任务执行
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }, "VirtualThread-" + i).start();
        }
        Thread.sleep(2000);
    }
}

### 对 switch 表达式和语句的模式匹配

解释: 

- 在 switch 表达式中，通过模式匹配可以更简洁地处理不同类型的数据。当 `obj` 是字符串时，会匹配到 `String s` 模式并执行相应代码，输出字符串的长度。


例子

- 


javapublic class SwitchPatternMatchingExample {
    public static void main(String[] args) {
        Object obj = "Hello";
        String result = switch (obj) {
            case String s -> "String: " + s.length();
            case Integer i -> "Integer: " + i;
            default -> "Unknown type";
        };
        System.out.println(result);
    }
}

### Vector API

解释: 

- 使用 Vector API 可以在运行时将向量计算可靠地编译为支持的 CPU 架构上的最佳向量指令，实现优于等效标量计算的性能。在这个例子中，通过向量化的方式对两个整数数组进行加法运算，提高了计算效率。


例子

- 


javaimport jdk.incubator.vector.*;

public class VectorExample {
    public static void main(String[] args) {
        int[] a = {1, 2, 3, 4, 5, 6, 7, 8};
        int[] b = {8, 7, 6, 5, 4, 3, 2, 1};
        int[] c = new int[8];
        IntVector va = IntVector.fromArray(IntVector.SPECIES_256, a, 0);
        IntVector vb = IntVector.fromArray(IntVector.SPECIES_256, b, 0);
        IntVector vc = va.add(vb);
        vc.intoArray(c, 0);
        System.out.println("Vector addition result: " + java.util.Arrays.toString(c));
    }
}

### Linux/RISC-V 移植

解释: 

- Java 对 Linux/RISC-V 的支持使得 Java 开发者可以在基于 RISC-V 指令集的硬件上运行 Java 程序。RISC-V 是一种开源的指令集架构，具有简洁、模块化、可扩展等特点，Java 对其支持将促进 Java 在更多硬件平台上的应用，特别是在一些对成本、功耗等有要求的嵌入式设备或新兴计算平台上。


## JDK 20

Java 20 共带来 7 个新特性功能，其中三个是孵化提案，孵化也就是说尚在征求意见阶段，未来可能会删除此功能。

### 作用域值（孵化器）

解释: 

- 作用域值是一种线程本地存储的替代方案，在并发编程中，它可以更安全地存储和传递数据。通过 `scopeValue.scope()` 方法创建一个作用域，然后在该作用域内可以使用 `scopeValue.get()` 获取值，当退出作用域时，资源会自动释放。与传统的线程本地存储相比，作用域值的作用域更加明确和可控，可以避免数据泄露等问题。


例子

- 


javaimport java.lang SCOPEVALUES;

public class ScopeValueExample {
    private static final ScopeValue scopeValue = ScopeValue.newScopeValue();

    public static void main(String[] args) {
        try (var sc = scopeValue.scope("Hello, World!")) {
            System.out.println(scopeValue.get()); // Hello, World!
        }
    }
}

### Record 模式匹配（第二次预览）

解释: 

- Record 模式匹配可以让我们更方便地解构记录类型对象。在 `instanceof` 表达式中，如果对象是指定记录类型，就可以直接获取记录的组件值，而无需先进行类型转换再访问成员变量，简化了代码，提高了开发效率，增强了代码的可读性。


例子

- 


javapublic record Point(int x, int y) {}

public class RecordPatternMatchingExample {
    public static void main(String[] args) {
        Object p = new Point(1, 2);
        if (p instanceof Point(int x, int y)) {
            System.out.println("x: " + x + ", y: " + y); // x:1,y:2
        }
    }
}

### switch 的模式匹配（第四次预览）

解释: 

- switch 的模式匹配允许在 switch 表达式或语句中使用模式匹配，根据对象的类型或结构进行不同的处理。它将传统的 switch 语句与模式匹配相结合，使得代码更加简洁、清晰，能够更方便地处理各种不同类型的数据，增强了代码的灵活性和可维护性。


例子

javapublic class SwitchPatternMatchingExample {
    public static String processObject(Object obj) {
        return switch (obj) {
            case Integer i -> "Integer: " + i;
            case String s -> "String: " + s;
            case Point p -> "Point: " + p.x() + ", " + p.y();
            default -> "Unknown object";
        };
    }

    public static void main(String[] args) {
        System.out.println(processObject(123)); // Integer:123
        System.out.println(processObject("Hello")); // String: Hello
        System.out.println(processObject(new Point(1, 2))); // Point:1,2
    }
}

### 外部函数和内存 API（第二个预览版）

解释: 

- 该 API 提供了一种更高效、更安全的方式来调用外部函数和处理外部内存。通过 `Arena` 管理内存生命周期，避免了内存泄漏等问题。它允许 Java 程序直接与外部系统进行交互，提高了 Java 与其他语言和系统集成的能力，使得 Java 能够更方便地利用外部资源和库，扩展了 Java 的应用场景。


例子

javaimport jdk.incubator.foreign.*;

public class ExternalFunctionAndMemoryAPIExample {
    public static void main(String[] args) throws Throwable {
       Arena arena = Arena.ofConfined();
        try {
            MemorySegment hello = arena.allocateUtf8String("Hello from native!");
            SystemLib.C().puts(hello);
        } finally {
            arena.close();
        }
    }
}

### 虚拟线程（第二个预览版）

解释: 

- 虚拟线程是轻量级的线程，由 JDK 管理，与传统的 OS 线程相比，创建和切换的成本更低。在大量并发任务场景下，可以大大提高程序的性能和可扩展性。上述代码中创建了大量虚拟线程来执行任务，而不用担心系统资源被过多占用。


例子

javapublic class VirtualThreadExample {
    public static void main(String[] args) {
        for (int i = 0; i < 1000; i++) {
            new Thread(() -> {
                try {
                    Thread.sleep(1000);
                    System.out.println("Task completed by " + Thread.currentThread().getName());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }, "VirtualThread-" + i).start();
        }
    }
}

### 结构化并发（第二孵化器）

解释: 

- 结构化并发提供了一种更安全、更方便的方式来管理并发任务。通过 `StructuredTaskScope`，可以将多个并发任务组织在一起，形成一个结构化的任务组。可以使用 `fork()` 方法启动并发任务，并通过 `join()` 方法等待所有任务完成。这种结构化的方式有助于避免并发编程中的常见问题，如死锁、资源泄漏等，提高了并发程序的可靠性和可维护性。


例子

javaimport jdk.incubator.concurent.*;

public class StructuredConcurrencyExample {
    public static void main(String[] args) {
        try (var scope = new StructuredTaskScope.<Void>newScope()) {
            Future<Void> f1 = scope.fork(() -> {
                // Task 1
                System.out.println("Task 1 started");
                Thread.sleep(1000);
                System.out.println("Task 1 completed");
            });

            Future<Void> f2 = scope.fork(() -> {
                // Task 2
                System.out.println("Task 2 started");
                Thread.sleep(1000);
                System.out.println("Task 2 completed");
            });

            scope.join(); // Wait for all tasks to complete
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}

### Vector API（第五孵化器）

解释: 

- Vector API 允许 Java 程序直接利用现代 CPU 的矢量指令集，实现对向量数据的高效处理。可以创建向量对象，然后使用各种向量操作方法（如加法、乘法等）对向量进行批量计算。这在科学计算、数据处理等领域可以大大提高程序的性能，因为向量操作可以并行处理多个数据元素，充分利用 CPU 的并行计算能力。


例子

javaimport jdk.incubator.vector.*;

public class VectorAPIExample {
    public static void main(String[] args) {
        IntVector.VectorSpecies vs = IntVector.SPECIES_256;
        IntVector a = IntVector.fromArray(vs, new int[]{1, 2, 3, 4, 5, 6, 7, 8});
        IntVector b = IntVector.fromArray(vs, new int[]{8, 7, 6, 5, 4, 3, 2, 1});
        IntVector c = a.add(b);
        int[] result = new int[c.length()];
        c.intoArray(result);
        System.out.println(Arrays.toString(result)); // [9,9,9,9,9,9,9,9]
    }
}

## JDK 21

根据发布的规划，这次发布的 JDK 21 将是一个长期支持版（LTS 版）。LTS 版每 2 年发布一个，上一次长期支持版是 21 年 9 月发布的 JDK 17。

### 序列集合

解释: 

- 当需要一个元素有明确顺序且该顺序是集合结构属性的集合时，序列集合就很有用。它弥补了集合框架中缺乏预定义顺序和统一操作集的不足，通过 `addFirst` 和 `addLast` 方法可以方便地在序列集合的头部或尾部添加元素，保持元素的有序性。


例子

java// 使用新的序列集合接口（假设为 SequencedCollection）
SequencedCollection<Integer> sequencedList = SequencedCollection.from(List.of(1, 2, 3,4));
sequencedList.addFirst(0);
sequencedList.addLast(5);
System.out.println(sequencedList);  // 输出：[0, 1, 2, 3,4,5]

### 分代 ZGC

解释

- 分代 ZGC 使 ZGC 能够维护年轻对象和年老对象的独立代。年轻对象通常生命周期短，分代后可以更频繁地收集年轻代对象，减少垃圾回收对应用程序性能的影响，提高整体性能。


例子

- 在使用 ZGC 的应用程序中，通过设置相关参数启用分代功能（具体参数依据实际 JEP 实施而定）。


java// 启用分代 ZGC 的 JVM 参数示例
-XX:+UseZGC -XX:+ZGenerational

### 记录模式

解释: 

- 使用记录模式（Record Patterns）增强Java编程语言，以解构记录值。可以嵌套记录模式和类型模式，以实现功能强大、声明性和可组合形式的数据导航和处理。


例子

- 记录模式允许我们通过 `instanceof` 结构结合记录类型，解构记录值。在例子中，通过 `p instanceof Point(int x, int y)` 既能判断 `p` 是否为 `Point` 类型，又能提取出其 `x` 和 `y` 分量，方便对记录中的数据进行处理


javapublic record Point(int x, int y) {}

public class Main {
    public static void main(String[] args) {
        Point p = new Point(1, 2);
        // 使用记录模式解构记录值
        if (p instanceof Point(int x, int y)) {
            System.out.println("x: " + x + ", y: " + y);
        }
    }
}

### switch 模式匹配

解释: 

- 通过switch表达式和语句的模式匹配来增强Java编程语言。通过将模式匹配扩展到switch，可以针对多个模式测试表达式，每个模式都有一个特定的操作，从而可以简洁、安全地表达复杂的面向数据的查询。


例子

- 通过将模式匹配扩展到 switch，可以让表达式针对多个模式进行测试。在这个例子中，`obj` 分别与 `String` 和 `Integer` 模式匹配，根据匹配结果执行对应的操作，从而简洁地处理不同类型的数据，实现复杂面向数据的查询。


javapublic class Main {
    public static void main(String[] args) {
        Object obj = "Hello";
        // 使用 switch 表达式和模式匹配
        String result = switch (obj) {
            case String s -> "String: " + s.length();
            case Integer i -> "Integer: " + i;
            default -> "Other";
        };
        System.out.println(result);  // 输出：String: 5
    }
}

### 虚拟线程

解释: 

- 将虚拟线程（Virtual Threads）引入Java平台。虚拟线程是轻量级线程，可以显著减少编写、维护和观察高吞吐量并发应用程序的工作量


例子

- 虚拟线程是轻量级线程，可以大量创建。在高并发场景下，使用虚拟线程能显著减少编写、维护和观察高吞吐量并发应用程序的工作量，因为它们的调度和管理由 JVM 自动处理，不像传统线程那样需要开发者手动处理复杂的并发问题。


javapublic class Main {
    public static void main(String[] args) {
        // 启动一个虚拟线程
        Thread.startVirtualThread(() -> {
            for (int i = 0; i < 5; i++) {
                System.out.println("Virtual Thread: " + i);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });
    }
}

### 弃用Windows 32位x86移植，并打算在将来的版本中将其删除

### 准备禁止动态加载代理

解释: 

- 将代理动态加载到正在运行的JVM中时发出警告。这些警告旨在让用户为将来的版本做好准备，该版本默认情况下不允许动态加载代理，以提高默认情况下的完整性。在启动时加载代理的可服务性工具不会导致在任何版本中发出警告


### 密钥封装机制 API

解释: 

- 介绍一种用于密钥封装机制（Key Encapsulation Mechanism，简称KEM）的API，这是一种使用公钥加密来保护对称密钥的加密技术。


例子

- **标准化支持** ：KEM API 遵循国际标准化组织（ISO）和国际电工委员会（IEC）制定的相关标准，确保了与全球安全标准的兼容性，这使得使用该 API 的 Java 应用能够在全球范围内更好地与其他系统和应用进行安全互操作。
- **灵活的密钥管理** ：提供了灵活的密钥封装和解封装功能，开发者可以根据具体需求选择合适的密钥封装算法和参数，如 RSA、EC-Kyber、DH 等算法，以及不同的密钥长度、加密模式等，以满足不同场景下的安全性、性能和兼容性要求。
- **强大的安全性** ：采用了先进的加密算法和协议，确保了密钥在传输过程中的安全性和完整性。例如，其通过公钥加密技术避免了传统密钥交换方法中填充的需求，降低了安全风险。同时，它还支持多种安全特性，如抗量子能力，能够应对未来可能出现的量子计算威胁，为长期的安全保障。
- **易于集成** ：KEM API 与 Java 的现有安全框架无缝集成，开发者可以轻松地将 KEM 功能集成到现有的 Java 应用程序中，无需对现有系统进行大规模的改造，降低了开发成本，提高了开发效率。


javaimport javax.crypto.KeyEncapsulationMechanism;
import javax.crypto.KEM;
import java.security.KeyPair;
import java.security.KeyPairGenerator;

public class KEMExample {
    public static void main(String[] args) throws Exception {
        // 接收方生成密钥对
        KeyPairGenerator kpg = KeyPairGenerator.getInstance("Kyber");
        KeyPair kp = kpg.generateKeyPair();

        // 发送方封装密钥
        KEM kem = KEM.getInstance("Kyber");
        KEM.Encapsulator enc = kem.newEncapsulator(kp.getPublic());
        KEM.Encapsulated e = enc.encapsulate();
        byte[] ciphertext = e.encapsulation(); // 发送给接收方
        byte[] secretKey = e.secret(); // 内容密钥

        // 接收方解封装
        KEM.Decapsulator dec = kem.newDecapsulator(kp.getPrivate());
        byte[] secretKey2 = dec.decapsulate(ciphertext); // 应与 secretKey 相同

        // 验证解密后的内容密钥是否与原始密钥相同
        System.out.println("原始密钥: " + new String(secretKey));
        System.out.println("解密后的密钥: " + new String(secretKey2));
        System.out.println("密钥是否相同: " + java.util.Arrays.equals(secretKey, secretKey2));
    }
}

### 字符串模板（预览）

解释: 

- 使用字符串模板（String Templates）增强Java编程语言。字符串模板通过将文本与嵌入的表达式和模板处理器耦合来生成专门的结果，从而补充Java现有的字符串文本和文本块。这是一个预览语言功能和API


例子

- 字符串模板通过将文本与嵌入的表达式和模板处理器耦合来生成专门的结果。在这里，`$x` 和 `$y` 分别代表变量 `x` 和 `y` 的值，`${x + y}` 则代表表达式 `x + y` 的结果，这样可以方便地在字符串中嵌入动态数据，使字符串的构建更加灵活和直观。不过需要注意的是，这个特性目前是预览版本，需要在启用预览功能的情况下才能使用。


javapublic class Main {
    public static void main(String[] args) {
        int x = 10;
        int y = 20;
        // 使用字符串模板
        String str = String.template("The sum of $x and $y is ${x + y}");
        System.out.println(str);  // 输出：The sum of 10 and 20 is 30
    }
}

### 外部函数和内存 API（第三次预览）

解释: 

- 引入API，Java程序可以通过该API与Java运行时之外的代码和数据进行互操作。通过有效地调用外部函数（即JVM外部的代码），并通过安全地访问外部内存（即不受JVM管理的内存），API使Java程序能够调用本机库并处理本机数据，而不会出现JNI的脆弱性和危险性。这是一个预览API


例子

- 这个预览 API 允许 Java 程序与外部代码和数据进行互操作。`MemorySegment` 表示外部内存区域，通过它可以访问外部函数。在这个例子中，假设有一个外部系统时间获取函数，我们可以通过获取其内存段，然后调用 `invoke` 方法来调用这个外部函数并获取结果，这样就避免了使用传统复杂且容易出错的 JNI（Java Native Interface）方式。


java// 这是一个简化的例子，实际使用较为复杂
// 假设有一个外部函数可以获取系统时间
MemorySegment systemTimeFunc = ...; // 获取外部函数的内存段
long currentTime = systemTimeFunc.invoke();
System.out.println("Current time from native function: " + currentTime);

### 未命名模式和变量（预览）

解释: 

- 使用未命名模式和未命名变量来增强Java语言，未命名模式匹配记录组件而不说明组件的名称或类型，未命名变量可以初始化但不使用。两者都用下划线字符_表示。这是一个预览语言功能。


例子

- 在 `switch` 表达式中，使用未命名模式（`Person(_, 30)`）来匹配记录组件年龄为 30 的情况，这里下划线表示对姓名这个组件不关心，不需要为其指定变量名。而 `case Person(var name, var age)` 则是用命名模式来获取姓名和年龄的值，用未命名模式可以简化代码，当对某些组件值不感兴趣时，就不必为其定义变量名。


javarecord Person(String name, int age) {}
Person alice = new Person("Alice", 30);
switch(alice)
{
    case Person(_, 30) -> System.out.println("Person is 30 years old");
    case Person(var name, var age) -> System.out.println("Name: " + name + ", Age: " + age);
}

### 未命名类和实例主方法（预览）

解释: 

- 发展Java语言，使学生无需理解为大型程序设计的语言功能即可编写第一个程序。学生们不用使用单独的Java方言，就可以为单类程序编写精简的声明，然后随着技能的发展，无缝地扩展程序，使用更高级的功能。这是一个预览语言功能。


例子

- 对于简单的程序，可以使用未命名类。在这个例子中，直接使用 `java` 命令后面跟一个类似于类名的结构，但并没有显式定义类。`main` 方法作为程序的入口点，这种方式可以让初学者更快速地编写和运行简单的 Java 程序，不需要了解复杂的类定义等概念，随着技能提升可以逐步过渡到更复杂的类和程序结构。


javajava example.Main { // 这里没有类定义
    public static void main(String[] args) {
        System.out.println("Hello, world!");
    }
}

### 作用域值（预览）

解释: 

- 引入作用域值，这些值可以在不使用方法参数的情况下安全有效地共享给方法。它们优先于线程化局部变量，尤其是在使用大量虚拟线程时。这是一个预览API


例子

- 作用域值（预览 API）可以在特定的作用域内共享值。首先通过 `ScopedValue.newInstance()` 创建一个作用域值实例。然后使用 `open` 方法打开一个作用域，将当前线程作为值与这个作用域关联。在这个作用域内，可以通过 `get` 方法获取与该作用域关联的值（当前线程），并且不需要通过方法参数来传递，这样在使用大量虚拟线程等场景下可以安全有效地共享数据。


javaScopedValue<Thread> currentThread = ScopedValue.newInstance();
try (Scope closeable = currentThread.open(Thread.currentThread())) {
    System.out.println("Current thread in scope: " + currentThread.get());
    // 在这个作用域内访问线程信息
}

### 结构化并发（预览）

解释: 

- 通过引入用于结构化并发（Structured Concurrency）的API来简化并发编程。结构化并发将在不同线程中运行的相关任务组视为单个工作单元，从而简化错误处理和消除，提高可靠性，并增强可观察性。这是一个预览API


例子

- 结构化并发（预览 API）将相关任务视为一个工作单元。在这个例子中，使用虚拟线程执行器来执行多个任务。当任何一个任务（如任务 3）出现错误时，整个工作单元会被视为失败，可以集中进行错误处理。并且结构化并发能够更好地观察任务的执行情况，提高并发编程的可靠性和可观察性。


javatry (var executor = Executors.newVirtualThreadExecutor()) {
    executor.execute(() -> {
        try {
            System.out.println("Task 1 started");
            Thread.sleep(2000);
            System.out.println("Task 1 completed");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    });
    executor.execute(() -> {
        try {
            System.out.println("Task 2 started");
            Thread.sleep(1000);
            System.out.println("Task 2 completed");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    });
    executor.execute(() -> {
        System.out.println("Task 3 started");
        // 假设任务 3 出现错误
        throw new RuntimeException("Task 3 failed");
    });
} catch (Exception e) {
    System.out.println("Error occurred: " + e.getMessage());
}

### Vector API（孵化器第六阶段）

例子

- Vector API 处于孵化器阶段，可以用于编写适用于 CPU 架构特定矢量指令集的代码。在这里，首先获取 `Float` 类型的矢量物种。然后定义一个操作（将每个元素乘以 2）。创建一个浮点向量 `vector1`，包含一些浮点数值。使用 `apply` 方法将操作应用到向量 `vector1` 上，得到新的向量 `vector2`。最后将向量 `vector2` 的内容转换为数组。这样可以利用矢量操作来加速数值计算等场景。


javaVectorSpecies<Float> floatSpecies = FloatVector.SPECIES_PREFERRED;
IntUnaryOperator op = i -> i * 2;
FloatVector vector1 = FloatVector.fromArray(floatSpecies, new float[]{1.0f, 2.0f, 3.0f});
FloatVector vector2 = vector1.apply(op);
vector2.intoArray(new float[floatSpecies.length()]);