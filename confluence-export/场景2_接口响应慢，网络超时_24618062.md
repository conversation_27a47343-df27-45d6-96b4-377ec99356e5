# 场景2: 接口响应慢，网络超时

## 一、排查方向

后端接口响应慢分以下2种情况：

1. 个别接口响应慢
  1. 本文只探讨这情况：排查个别接口响应慢的方法。
2. 本文只探讨这情况：排查个别接口响应慢的方法。
3. 所有接口响应慢
  1. 可能是服务器问题，需要排查网络、CPU使用率、内存使用率、磁盘使用率等。
4. 可能是服务器问题，需要排查网络、CPU使用率、内存使用率、磁盘使用率等。


## 二、定位问题

### 2.1 获取traceId

- 通过请求响应头获取TraceId


- 通过TLS找到对应请求日志（[生产环境日志平台查询手册 - 运维组 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6361781)），在日志中获取TraceId


### 2.2 在skywalking中查看请求链路，分析接口耗时原因（链路追踪Skywalking - 后端研发部 - Confluence (hxyxt.com)）

skywalking会展示出每一个与网络有关的耗时，比如：读写数据库、读写Redis、SpringCloud调用、Dubbo调用等。这样就能立马定位是哪次操作耗时了。

同时，SkyWalking可以记录每一个SQL语句，可以帮助定位。

例如：（最上边一个是总耗时，下边的线段是单个操作的耗时）点击耗时慢的数据库步骤可以查看详情

## 三、解决问题

### 常见问题：

1. **数据库耗时长**
  - 必要字段加索引
  - 确定是否索引失效了
  - 如果有回表查询，尽量优化为覆盖索引
2. 必要字段加索引
3. 确定是否索引失效了
4. 如果有回表查询，尽量优化为覆盖索引
5. **业务逻辑**
  - 死循环
  - 循环PRC（接口调用/数据库请求）
  - 锁
6. 死循环
7. 循环PRC（接口调用/数据库请求）
8. 锁