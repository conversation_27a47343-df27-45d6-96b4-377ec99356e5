# 场景1: Mysql 服务 CPU升高

#### 场景1 慢查询导致CPU升高

问题原因：大量慢SQL导致实例CPU升高，需要优化相应的慢SQL。

排查思路：

查看CPU使用率和慢日志个数统计监控指标。

- 如果慢日志个数很多，且与CPU曲线吻合，可以确定是慢SQL导致CPU升高。
- 如果慢日志个数不多，但与CPU使用率基本一致，进一步查看行读取速率指标是否与CPU曲线吻合。如果吻合，说明是少量慢SQL访问大量行数据导致CPU升高：由于这些慢SQL查询执行效率低，为获得预期的结果需要访问大量的数据导致平均IO高，因此在QPS并不高的情况下（例如网站访问量不大），也会导致实例的CPU使用率偏高。


解决方案：

1. 根据CPU使用率过高的时间点，查看对应时间段的慢日志信息。
2. 重点关注扫描行数、返回结果行数超过百万级别的慢查询，以及锁等待时间长的慢查询。
3. 慢查询用户可自行分析，或使用数据管理服务(DAS)的[SQL诊断工具](https://support.huaweicloud.com/usermanual-das/das_04_0100.html)对慢查询语句进行诊断。
4. 使用数据库代理+只读实例架构，实现读写分离。只读实例专门负责查询，减轻主库压力，提升数据库吞吐能力，详见[读写分离简介](https://support.huaweicloud.com/usermanual-rds/rds_11_0035.html)。
5. 通过分析数据库执行中的会话来定位执行效率低的SQL。
  1. 连接数据库。
  2. 执行**show full processlist;**。
  3. 分析执行时间长、运行状态为Sending data、Copying to tmp table、Copying to tmp table on disk、Sorting result、Using filesort的会话，均可能存在性能问题，通过会话来分析其正在执行的SQL。
6. 连接数据库。
7. 执行**show full processlist;**。
8. 分析执行时间长、运行状态为Sending data、Copying to tmp table、Copying to tmp table on disk、Sorting result、Using filesort的会话，均可能存在性能问题，通过会话来分析其正在执行的SQL。


#### 场景2 连接和QPS升高导致CPU上升

问题原因：业务请求增高导致实例CPU升高，需要从业务侧分析请求变化的原因。

排查思路：

查看QPS、当前活跃连接数、数据库总连接数、CPU使用率监控指标是否吻合。

QPS的含义是每秒查询数，QPS和当前活跃连接数同时上升，且QPS和CPU使用率曲线变化吻合，可以确定是业务请求增高导致CPU上升，如下图：

该场景下，SQL语句一般比较简单，执行效率也高，数据库侧优化余地小，需要从业务源头优化。

解决方案：

1. 单纯的QPS高导致CPU使用率过高，往往出现在实例规格较小的情况下。例如：1U、2U、4U，建议升级实例CPU规格。
2. 优化慢查询，优化方法参照[场景1 慢查询导致CPU升高](https://support.huaweicloud.com/trouble-rds/rds_12_0056.html#rds_12_0056__section35045128334)的解决方案。若优化慢查询后效果不明显，建议升级实例CPU规格。
3. 对于数据量大的表，建议通过分库分表减小单次查询访问的数据量。
4. 使用数据库代理+只读实例架构，实现读写分离。只读实例专门负责查询，减轻主库压力，提升数据库吞吐能力，详见[读写分离简介](https://support.huaweicloud.com/usermanual-rds/rds_11_0035.html)。