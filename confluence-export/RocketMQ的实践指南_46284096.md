# RocketMQ的实践指南

# 背景

在2024-10-17日下午6点半，距离美团迁移上线已经有一周多了，线上已切换的一家O2O网店和B2C网店的订单运行一切正常，于是决定将剩余两家O2O网店和一家B2C网店切到新接口中台。

在6点40左右将回调地址修改完毕，观察线上回调接口并无异常，但到了7点左右，有运营小伙伴反馈订单未进心云，经排查发现，MQ发生严重的消费延时问题。

# 原因分析

## 接口中台是如何处理回调消息的？

1. 详情参考这篇文章：[接口中台重构 V1.0 - 后端研发部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24609530)
2. 我们单独部署了一个服务（third-platform-callback-mt），用来接收美团的回调请求，当美团调用这个服务接口的时候，我们会快速根据不同域放到对应的MQ topic中，进行异步消费
3. 对于订单域的消息在另一个服务（third-platform-order-mt）中进行消费处理


## 为何会出现消费堆积问题？

### 常见的MQ消息堆积原因

1. 消费者处理消息的速度慢：
  1. 消费者的处理逻辑复杂或存在性能瓶颈，导致处理速度慢于消息的生产速度。
  2. 消费者的硬件资源（如CPU、内存）不足，影响处理效率。
2. 消费者的处理逻辑复杂或存在性能瓶颈，导致处理速度慢于消息的生产速度。
3. 消费者的硬件资源（如CPU、内存）不足，影响处理效率。
4. 生产者发送消息的速度过快：
  1. 在短时间内生产者发送的消息量远超消费者的处理能力，导致消息堆积。
5. 在短时间内生产者发送的消息量远超消费者的处理能力，导致消息堆积。
6. 消费者出现故障或宕机：
  1. 消费者服务出现故障或意外停机，无法处理队列中的消息，造成消息积压。
7. 消费者服务出现故障或意外停机，无法处理队列中的消息，造成消息积压。
8. 突发性消息堆积：
  1. 特定事件（如促销活动、新闻事件等）导致短时间内消息量激增，超出常规处理能力。
9. 特定事件（如促销活动、新闻事件等）导致短时间内消息量激增，超出常规处理能力。
10. 持续性消息堆积：
  1. 生产者持续产生消息的速度高于消费者的消费速度，长期下来形成消息积压。
11. 生产者持续产生消息的速度高于消费者的消费速度，长期下来形成消息积压。
12. 周期性消息堆积：
  1. 消息堆积呈现出一定的周期性，通常与系统负载的变化有关，比如工作日与周末、白天与夜晚的访问量差异。
13. 消息堆积呈现出一定的周期性，通常与系统负载的变化有关，比如工作日与周末、白天与夜晚的访问量差异。
14. 网络延迟或故障：
  1. 网络问题导致消费者无法及时从队列中拉取消息，影响消费效率。
15. 网络问题导致消费者无法及时从队列中拉取消息，影响消费效率。
16. 队列配置不当：
  1. 队列的并发处理设置不合理，如并发数设置过低，限制了消费者的处理能力。
17. 队列的并发处理设置不合理，如并发数设置过低，限制了消费者的处理能力。


### MQ消息堆积原因分析

#### 资源背景

1. 服务中RocketMQ版本
  1. 5.2.0
  2. rocketmq-spring-boot-starter：2.3.0
2. 5.2.0
3. rocketmq-spring-boot-starter：2.3.0
4. Topic：
  1. TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-MT_CALLBACK
  2. broker数：2
  3. 写/读队列数：3/broker
5. TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-MT_CALLBACK
6. broker数：2
7. 写/读队列数：3/broker
8. 消息生产端（third-platform-callback-mt）
  1. 2个实例
  2. MQ发送方式为普通同步方式发送
9. 2个实例
10. MQ发送方式为普通同步方式发送
11. 消息消费端（third-platform-order-mt）
  1. 2个实例
  2. 消费方式为CLUSTERING
  3. ConsumeThreadMax（最大消费线程数）：8
  4. ConsumeThreadMin（最大消费线程数）：1
  5. ConsumeMessageBatchMaxSize（每批次消息处理数量）：未设值，默认为1
  6. PullBatchSize（批量拉取数量）：未设值，默认为32
  7. 每条订单回调消息的处理时间：最慢不到1s，按1s/条计算
12. 2个实例
13. 消费方式为CLUSTERING
14. ConsumeThreadMax（最大消费线程数）：8
15. ConsumeThreadMin（最大消费线程数）：1
16. ConsumeMessageBatchMaxSize（每批次消息处理数量）：未设值，默认为1
17. PullBatchSize（批量拉取数量）：未设值，默认为32
18. 每条订单回调消息的处理时间：最慢不到1s，按1s/条计算


#### 第一次切店到第二次切换

在只切换了一家O2O网店和B2C网店的时候，共有大概800家门店，这些门店的订单回调消息大概是下图这个水平：

一分钟最大的回调消息总数只有56条，每秒消息数不到1条，按上面的资源，消费起来绰绰有余，未发现有消费堆积问题

#### 第二次切换

第二次切换回调配置之后，一共有1W+的门店，此时订单的回调消息大概如下图：

一分钟最大的回调消息数达到了705条，每秒消息数最大为12条，此时消费延时时间最大达到了5min+，堆积数量到了3700条，如下图：

#### 原因分析：

主要为 队列的并发处理设置不合理，如并发数设置过低，限制了消费者的处理能力。

1. ConsumeMessageBatchMaxSize（每批次消息处理数量）：未设值，默认为1，导致每次虽然拉取了32条数据，最大线程数设置了8，但是并不会8个线程一起处理，而是从始至终都是一个线程在消费


# 优化方案

## 资源及配置优化

1. 增加消费端（third-platform-order-mt）实例数到4个
2. 修改topic的读/写队列数为 4/broker
3. 修改ConsumeThreadMax（最大消费线程数）：32
4. 修改ConsumeThreadMin（最大消费线程数）：32
5. 修改ConsumeMessageBatchMaxSize（每批次消息处理数量）：32
6. PullBatchSize（批量拉取数量）：32


## 注意点

1. 读/写队列数需要保持一致
2. pullBatchSize的大小受制于broker配置文件中 maxTransferCountOnMessageInMemory 参数的设置，该参数默认设置为 32，也即是每次从服务端拉取的最大的数量不能超过32，因此即使设置 pullBatchSize超过32，最后也只返回32。
3. consumeMessageBatchMaxSize 也跟pullBatchSize相关，消费线程实际上每次消费的消息数量不会大于 pullBatchSize


# 优化结果

1. 压测结果：
  1. 设置回调消息生产数量为 100/s，每分钟生产 6000 条，持续执行2.5分钟，共发送15000+条消息
    1. 
    2. 
  2. 
  3. 
  4. 消费端的处理时间模拟为800ms/条，结果如下：
    1. 
    2. 
    3. 
  5. 
  6. 
  7. 
  8. 可以看出在100qps的请求下，是毫无压力的
  1. 
  2. 
  1. 
  2. 
  3. 
2. 设置回调消息生产数量为 100/s，每分钟生产 6000 条，持续执行2.5分钟，共发送15000+条消息
  1. 
  2. 
3. 
4. 
5. 消费端的处理时间模拟为800ms/条，结果如下：
  1. 
  2. 
  3. 
6. 
7. 
8. 
9. 可以看出在100qps的请求下，是毫无压力的
10. 线上运行结果：
  1. 
  2. 
  3. 
11. 
12. 
13.