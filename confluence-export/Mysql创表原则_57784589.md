# Mysql创表原则

 1.每张表有系统时间及版本号
 2.每张表关联字段使用唯一号 避免使用主键ID
 3.主表增加年月日字段 方便查询.
 4.提前增加拓展字段
 5.字段长度设置 
 一般长度 varchar(20)
 唯一号长度 varchar(50)
 不确定信息 varchar(255)
 特长 varchar(1000)
 
 金额相关 decimal(16, 6)

 6.枚举及布尔值都用英文单词.




主表
 
 
 `mer_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商户编码',
 `company_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司编码',
 `company_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司名称',
 `organization_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属机构编码',
 `organization_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属机构名称',
 `org_parent_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织机构父路径id链路 1000-1100-1110-',
 `is_valid` bigint NOT NULL DEFAULT '0' COMMENT '是否起效 1-起效 -1-未起效 ',
 `created_day` date DEFAULT NULL COMMENT '平台创建日',
 `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
 `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
 `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
 `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
 `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
 `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',

主表的拓展表


 `extend_json` json DEFAULT NULL COMMENT '拓展字段',
 `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
 `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
 `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
 `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
 `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
 `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',


子表
 


 `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
 `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
 `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
 `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
 `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
 `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',

子表的拓展表

 `extend_json` json DEFAULT NULL COMMENT '拓展字段',
 `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
 `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
 `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
 `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
 `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
 `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',

基础表

 `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
 `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
 `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
 `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
 `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
 `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',