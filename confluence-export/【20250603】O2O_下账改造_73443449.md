# 【20250603】O2O  下账改造

# 一、现状及目标

 （一）现状

 目前O2O 订单模块缺少下账单概念，数据传递在系统之间没有留存。特别是针对退款业务部分核心信息丢失。无法满足部分分公司的下账要求。

 （二）目标

 构建一个独立的下账模块能够承接020,B2C以及未来可能出现的其他业务类型的下账数据保存、业务流转机及其下游系统交互。

 本次只针对改造020下账，其他模块预留插槽。数据结构统一使用order_account 数据库

# 二、系统架构设计

## （一）服务拆分


1. **订单服务（business-order）**
 - 保持现有订单履约流程的处理逻辑，不涉及下账单的直接处理。
 - 在关键节点（拣货完成、退款单审核通过、运维工具修改已经完成的订单为已取消、修改下账单信息）通过MQ通知下账单服务。


2. **下账单服务（billing-service）**
 - 新增服务，专门负责下账单的创建、修改、查询、下账等操作。
 - 提供接口供订单服务调用，接收订单履约事件并生成下账单。
 - 调用第三方接口完成下账操作，并处理下账结果反馈。

## （二）消息中间件


- 使用消息队列（Rocket MQ）作为订单服务与下账单服务之间的通信桥梁。
- 订单服务在关键节点发布消息到指定队列，下账单服务监听队列中的消息并进行处理。

## （三）第三方接口集成


- 下账单服务封装第三方接口调用逻辑，根据下账单信息调用第三方接口完成下账操作。
- 对第三方接口的返回结果进行处理，更新下账单状态，并记录日志。

# 三、交互时序图

# 四、功能模块明细设计

## （一）下账单生成模块


1. **正向单生成**
 - 监听订单服务发布的拣货完成或配送出库事件消息。
 - 根据事件消息中的订单信息，从订单数据库查询订单详情。
 - 根据订单信息生成下账单主表记录、下账商品明细表记录和下账金额信息表记录。
 - 设置下账单状态为“待下账”，并存储到下账单数据库。


2. **逆向单生成**
 - 监听订单服务发布的售后单审核完成事件消息。
 - 根据事件消息中的售后单信息，查询对应的正向下账单。
 - 根据正向下账单信息生成逆向下账单，设置下账单状态为“待下账”，并存储到下账单数据库。


3. **运费单生成**
 - 监听订单服务发布的运费单生成事件消息。
 - 根据事件消息中的运费单信息，生成运费下账单，批号设置为“00”，并存储到下账单数据库。


4. **运维工具手工单生成**
 - 提供手工单生成接口，接收运维工具传入的销售单和拣货明细信息。

 - 手动修改正单下账完成的订单状态为已取消同时会生成逆向单。审核完成后也需要同步生成逆向单下账单。
 - 根据传入的信息生成下账单，并存储到下账单数据库。

## （二）下账单查询模块


1. **下账单列表查询**
 - 提供接口供前端调用，根据查询条件（如子公司维度、下账单号等）从下账单数据库查询下账单列表。

 - 下账列表数查询ES，详情直接查询mysql 数据库（由于查询对时效性要求不高， 查询走从库）。
 - 返回下账单列表，包括下账单号、平台订单号、系统订单号、店铺信息、下单门店信息、下账门店信息、下账单状态等字段。


2. **下账单详情查询**
 - 提供接口供前端调用，根据下账单号从下账单数据库查询下账单详情。
 - 返回下账单详情，包括基本信息、下账商品信息、下账金额信息等。

## （三）下账单修改模块


- 提供接口供运维工具调用，修改下账单信息。
- 修改下账单的本质是修改发货单的拣货明细表（由于当前O2O没有发货单，所以直接修改拣货明细表）。
- 同时将修改日志更新到订单上和发货单上（虽然当前O2O没有发货单，但预留接口供未来扩展）。
- 更新下账单数据库中的下账单记录和下账单日志表记录。

## （四）下账模块


1. **下账操作**
 - 触发机制调用下账接口，查询下账单数据库中状态为“待下账”的下账单。
 - 根据下账单信息调用第三方接口完成下账操作。
 - 根据第三方接口返回结果更新下账单状态：
 - 下账成功，将下账单状态更新为“已下账”。
 - 下账失败，将下账单状态更新为“下账失败”，并记录失败原因。


2. **下账结果反馈**
 - 将下账结果反馈给订单服务，更新订单状态。
 - 记录下账结果到下账单日志表。

# 五、billing-service 整体结构图

# 六、数据库设计

采用系统现有的order_account 数据库，其中主要包含

```
account_order 下账正单主表信息
```

```
account_order_detail 下账商品信息
```

```
account_order_detail_trace 下账追溯码信息 account_order_detail_pick 下账拣货信息
```

```
account_order_pay 下账金额信息
```

```
account_refund 下账退款主表信息
```

```
account_refund_detail 下账退款商品信息
```

```
account_refund_detail_trace 下账退款追溯码信息 account_refund_detail_pick 下账退款拣货信息
```

```
account_refund_pay 下账退款金额信息
```

```

```