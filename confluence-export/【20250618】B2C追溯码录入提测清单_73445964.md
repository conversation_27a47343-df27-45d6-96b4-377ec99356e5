# 【20250618】B2C追溯码录入提测清单

## 一、提测信息

| **提测内容** | B2C追溯码录入1. 拣货复核 2. 扫描录入 3. 退货审核 4. 销售、售后下账单 |
| --- | --- |
| **提测Git项目****************** | 服务 | GitLab | 提测分支 | 主R |
| 后端：hydee-business-order-web | [https://yxtgit.hxyxt.com/order/hydee-business-order-web](https://yxtgit.hxyxt.com/order/hydee-business-order-web) | ``` test ``` |  |
| 前端：cloud-ui | [https://yxtgit.hxyxt.com/frontend-business/cloud-ui](https://yxtgit.hxyxt.com/frontend-business/cloud-ui) | release |  |
|  |  |  |  |
| **提测(上线)清单** | 待补充 |
| **提测时间** |  |
| **上线时间** |  |
| **产研负责人****** | 产品（PM） |  |
| 前端研发（RD） |  |
| 后端研发（RD） |  |
| 测试（QA） |  |
| 代码审查（Code Reviewer） |  |
| **产品PRD** | 一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5684 |
| **技术方案** |  |
| **是否测试介入** | 是 |
| *******回退方案** | 1. 代码中已做新老版本切换 2. 回退部署代码版本 |
| 备注 | 1. 验证新功能的追溯码录入是否正常。 2. 验证历史功能是否正常录入批号，正常状态流转 |


## 二、里程碑

| 里程碑 | 说明 |
| --- | --- |
|  | 技术方案评审 |
|  | 开发+自测完成 |
|  | 联调完成，提测 |
|  | 正式发版 |


## 三、单元测试

| 单测描述项目 | 结果确认 | 备注（说明或者截图） |
| --- | --- | --- |
| **[新增/变更代码]** **确认有单测覆盖** | 满足 | 本次迭代新增、变更代码未有对应单测，单测均执行成功。 |
| **[新增/变更代码] 单测行覆盖率>70%** | 满足 | 本次迭代新增、变更代码核心代码单测行覆盖率>90% |
| **Sonar代码确认无阻断性问题** | 满足 |  |