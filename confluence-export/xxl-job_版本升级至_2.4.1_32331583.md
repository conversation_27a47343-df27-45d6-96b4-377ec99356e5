# xxl-job 版本升级至 2.4.1

## 一、背景

 XXL-JOB 2.4.0及之前版本出现安全漏洞，且我们的xxl-job 版本为2.1.1。与运维沟通后需要升级 xxl-job 版本至2.4.1.

 XXL-JOB 默认配置下，用于调度通讯的 accessToken 不是随机生成的，而是使用 application.properties 配置文件中的默认值。在实际使用中如果没有修改默认值，攻击者可利用此绕过认证调用 executor，执行任意代码，从而获取服务器权限。

 原文链接 [https://mp.weixin.qq.com/s/xnkVM2N12qb2azrLcv93fg](https://mp.weixin.qq.com/s/xnkVM2N12qb2azrLcv93fg)

## 二、XXL-JOB 新环境地址

| 环境 | url |
| --- | --- |
| 开发 | [https://dev-xxl-job-new.hxyxt.com/xxl-job-admin/](https://dev-xxl-job-new.hxyxt.com/) |
| 测试 | [https://test-xxl-job-new.hxyxt.com/](https://test-xxl-job-new.hxyxt.com/)xxl-job-admin/ |
| 预发 | - |
| 生产 | [https://pro-xxl-job-new.hxyxt.com/xxl-job-admin/](https://pro-xxl-job-new.hxyxt.com/) |


执行器注册地址各环境使用内网统一地址： [http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/)

*本地启动请使用外网地址

## 三、升级内容

|  | 内容 | 备注 |
| --- | --- | --- |
| 版本升级 | xxl-job-admin 版本升级至 2.4.1 | 接入 xxl-job 项目统一升级 xxl-job-core 版本为 2.4.1 |
| 账号权限 | 登录账号对接ldap，使用心云账号登录 | 首次登陆后找运维同学开通对应执行器权限 |
| 告警 | 增加企微机器人告警通知 |  |


## 四、升级影响

| 影响点 | 影响程度 | 内容 | 举例 |
| --- | --- | --- | --- |
| 任务开发方式 | Red严重 | 移除旧类注解JobHandler，推荐使用基于方法注解 “[@XxlJob](https://github.com/XxlJob)” 的方式进行任务开发。可以支持单个类中开发多个任务方法，进行类复用 | ``` @XxlJob("demoJobHandler") public void demoJobHandler() throws Exception {     // default success } ``` |
| 任务入参/反参 | Red严重 | 任务核心类 “IJobHandler” 的 “execute” 方法取消出入参设计。改为通过 “XxlJobHelper.getJobParam” 获取任务参数并替代方法入参，通过 “XxlJobHelper.handleSuccess/handleFail” 设置任务结果并替代方法出参 | ``` @XxlJob("demoJobHandler") public void execute() {     String param = XxlJobHelper.getJobParam();    // 获取参数     XxlJobHelper.handleSuccess();// 设置任务结果-成功     //XxlJobHelper.handleFail();// 设置任务结果-失败 } ``` |
| 日志输出 | Red严重 | ”XxlJobLogger” 组件废弃：改用 “XxlJobHelper.log” 进行日志输出； | ``` JobHelper.log("任务执行异常，原因：{}",e.message()); ``` |
| 执行器注册逻辑优化 | 轻度 | 新增配置项 ”注册地址 / xxl.job.executor.address“，优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。 |  |


## 五、升级流程

1. 升级xxl-job-core 包 可直接只用组件依赖


javaEmacs        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-xxljob-spring-boot-starter</artifactId>
            <version>4.1.0</version>
        </dependency>

 2. 配置文件

ymlxxl:
  job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin/
    executor:
      appname:
      port: 
      ip:
      address:
      logretentiondays: 7
    accessToken: sk_token   

 3. 修改任务开发方式

java原任务开发方式@JobHandler(value = "b2cSyncOrderJobHandler")
@Component
public class B2cSyncOrderJobHandler extends IJobHandler {

    @Autowired
    private OrderPullDispatcher orderPullDispatcher;

    @Override
    public ReturnT<String> execute(String merCode) throws Exception {
        XxlJobLogger.log("b2cOrderPullDispatch start......");
        try {
            orderPullDispatcher.action();
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return new ReturnT<String>(IJobHandler.FAIL.getCode(), "b2cOrderPullDispatch is failed");
        }
        XxlJobLogger.log("b2cOrderPullDispatch end......");
        return IJobHandler.SUCCESS;
    }
}

java新的任务开发方式@Component
public class B2cSyncOrderJobHandler {

    @Autowired
    private OrderPullDispatcher orderPullDispatcher;

    @XxlJob("b2cSyncOrderJobHandler") //声明任务类型
    public void b2cOrderPullDispatch() { //取消入参  出参 
        XxlJobHelper.log("b2cOrderPullDispatch start......"); //日志类使用 XxlJobHelper
        try {
            orderPullDispatcher.action();
        } catch (Exception e) {
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail("b2cOrderPullDispatch 执行失败"); //设置任务结果
            return;
        }
        XxlJobHelper.log("b2cOrderPullDispatch end......");
        XxlJobHelper.handleSuccess();//设置任务结果
    }

    @XxlJob("demo")
    public void demo() {
        String jobParam = XxlJobHelper.getJobParam(); //获取执行参数
        XxlJobHelper.log("XXL-JOB, Hello World.");
        XxlJobHelper.handleSuccess("success");  //设置任务结果
    }
    
}

## 六、H3项目接入办法

6.1 pom修改

| ``` <dependencies> 项目中用到了h3-hdframe-base 才需要排除它的xxl-job `````` <dependency>     <groupId>com.hydee.h3</groupId>     <artifactId>h3-hdframe-base</artifactId>     <version>${hydee.hdframe.version}</version>     <exclusions>         <exclusion>             <groupId>com.xuxueli</groupId>             <artifactId>xxl-job-core</artifactId>         </exclusion>         <exclusion>             <groupId>com.hydee.common</groupId>             <artifactId>common-job</artifactId>         </exclusion>     </exclusions> </dependency> ``````  `````` <!-- xxljob -->  <dependency>  <groupId>com.yxt</groupId>  <artifactId>yxt-xxljob-spring-boot-starter</artifactId>  <version>4.1.0</version>  </dependency> </dependencies> ``````  ``````  `````` <dependencyManagement>     <dependencies>         <dependency>             <groupId>com.xuxueli</groupId>             <artifactId>xxl-job-core</artifactId>             <version>2.4.1</version>         </dependency>     </dependencies> </dependencyManagement> ``````  ``` |
| --- |
|  |


6.2 移除代码中所有 @EnableXxlJob

6.3 新建class

如何项目启动器如下使用了 HydeeApplication 的，需要

在启动器所在module下创建下面的类

| ``` package com.hydee.h3; import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig; import org.springframework.boot.SpringApplication; import org.springframework.boot.autoconfigure.SpringBootApplication; import org.springframework.boot.web.servlet.ServletComponentScan; import org.springframework.cloud.client.discovery.EnableDiscoveryClient; import org.springframework.cloud.netflix.hystrix.EnableHystrix; import org.springframework.cloud.openfeign.EnableFeignClients; import org.springframework.scheduling.annotation.EnableAsync; import org.springframework.scheduling.annotation.EnableScheduling; /**  * @Description:  * <AUTHOR>  * @Date 2024/10/22 10:32  */ @EnableApolloConfig @EnableDiscoveryClient @EnableFeignClients @EnableHystrix @EnableScheduling @EnableAsync @SpringBootApplication @ServletComponentScan public class HydeeApplication {     public HydeeApplication() {     }     public static void run(String[] args) {         SpringApplication.run(com.hydee.h3.HydeeApplication.class, args);     } } ``` |
| --- |


6.4 修改因为升级所带来的代码变动。

见 5.3
6.5 h3测试实例