# 对接电商平台流程文档

# 

### 一、对接前准备工作

#### 1.1 接口确认

- 订单API
- 售后API
- 物流API
- 电子面单API


#### 1.2 消息确认

- 授权消息
- 订单消息
- 退款消息
- 商品消息


#### 1.3 开发准备

- 开放平台账号确认以及创建应用
- 消息订阅
- 加密算法
- 授权确认


### 二、对接流程

#### 2.1 对接方式选择

- HTTP：需要自定义请求和响应参数，以及签名算法
- SDK：快速使用，不需要多余定义


#### 2.2 授权（OAuth 2.0）

- 简单授权：打开授权URL，接口返回access token，更新对应数据配置
- 授权码：把回调URL拼接到授权URL，拿到授权码后调用对应的接口换取对应的access token


#### 2.3 消息回调

- HTTP回调消息接收
- 长连接（**WebSocket**）回调消息接收，可实现CommandLineRunner初始化长连接，参考拼多多对接


### 三、项目相关

| 项目名称 | 服务明细 | 功能实现 | 备注 |
| --- | --- | --- | --- |
| 接口中台 | third-platform-callback-pdd | 接收三方平台回调消息，如授权、订单、物流等消息，同时转发到下游服务处理 | 1.根据订单实际情况判断是否需要拆分模块，比如拼多多 2.目前拼多多订单直连B2C订单中台，无需通过O2O订单中台做转发操作3.目前订单消息、物流消息、鉴权消息只需更改Apollo配置和消息转发校验即可，不存在新业务情况下暂不需要创建新消息4.默认使用电子面单服务，如需使用物流中台，需要更改相关配置5.物流相关开发建议同时满足物流中台和电子面单服务，功能较为分散，没有做统一处理6.涉及SDK较多，做好版本记录，上线前优先对SDK进行打包更新相关服务依赖7.非直连情况，注意express_mapping表是否存在三方平台的快递映射，同时校验数据是否正确 |
| third-platform-order-pdd | 接收回调服务下发的订单消息，根据不同Tag做业务数据处理，发送订单消息 |
| third-platform-logistics | 接收回调服务下发的物流消息，根据不同Tag做业务数据处理，发送物流消息 |
| third-platform-other | 接收回调服务下发的授权消息，刷新token等相关操作 |
| B2C订单中台 | hydee-business-order-web | 1.接收接口中台-订单服务下发的订单消息用于订单创建、更新，退款单创建、更新2.调用接口中台的SDK实现退款审核、同步物流信息到平台（发货）功能3.调用物流中台或者电子面单服务（通过配置区分）实现面单创建、同步物流网点、模板功能 |
| hydee-business-order-b2c-third | 接收接口中台-订单服务下发的订单消息用于订单创建、更新，退款单创建、更新 |
| 物流中台 | logistics-center | 1.调用接口中台的SDK实现面单创建、更新、取消、拦截2.接收接口中台-物流服务下发的物流消息，获取物流轨迹、拦截结果等数据 |
| 电子面单服务 | hydee-oms-tool | 电子面单服务的基础SDK，区分创建面单通过海典实现还是电子面单服务实现 |
| hydee-oms-logistic | 电子面单创建、同步物流网点、电子面单模板 |


### 四、配置相关（配置可参考拼多多上线CheckLIst）

| 项目名称 | 服务明细 | 配置描述 |
| --- | --- | --- |
| 接口中台 | third-platform-callback-pdd | 定义转发下游的消息，订单、物流、授权消息 |
| third-platform-order-pdd | 定义接收回调服务下发的订单消息，以及转发到订单中台的订单消息 |
| third-platform-logistics | 定义回调服务下发的物流消息，轨迹推送和拦截推送 |
| third-platform-other | 定义回调服务下发的授权消息 |
| B2C订单中台 | hydee-business-order-webhydee-business-order-b2c-third | 1.定义接收接口中台-订单服务下发的订单消息2.migrated-config-list配置，用于判断是否走接口中台，如退款审核等3.logistic配置，电子面单服务配置 |
| 网关服务 | hydee-api-gateway | 配置回调接口服务以及接口路径，用于三方回调 |
| third-platform-gateway | 配置接口中台SDK路径，用于其他服务如订单中台、物流中台调用SDK |


### 五、数据流转图

 1.授权消息接收

 

![正单流程](./resources/正单流程.png)



 2.正单/退单消息接收

 

![订单(正单、退单)消息](./resources/订单(正单、退单)消息.png)



 3.物流消息接收

 

![物流消息](./resources/物流消息.png)



 4.订单同步平台

 

![订单同步平台](./resources/订单同步平台.png)



 5.电子面单

 

![电子面单](./resources/电子面单.png)

