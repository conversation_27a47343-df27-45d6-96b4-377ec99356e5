# Jackson的多态反序列化

# 背景

产品经理小凯在某一天找到了你，说，小明啊，有个新的需求，很简单，给门店配置支付方式，目前第一版本迭代，我们暂时只支持微信支付，我们需要保存的信息大概有以下几个：

小明在分析完需求之后，觉得这有何难，立马奋笔疾书，接口定义、入参实体定义、逻辑实现一气呵成。

这个时候的入参实体可能长这个样子：

1. 


然而没过两个月，小凯又来了，小明啊，新需求又来了，要新增一种医保支付方式，需要保存医保的信息如下:

小明接到需求之后，思索片刻，不就多了个类型，又有何难，缺啥字段补啥字段。

于是小明在第一版定义的接口入参中新增了医保相关的字段：

此时，入参实体已然有N多字段。

又过了不到两个月，小凯又过来了，小明啊，我们又又需要增加一个支付类型。。。。。。。。。。

# 优化方案

 问题其实很简单，随着业务的不断发展，接口的入参实体为了兼容多种支付类型，字段数量就会无限增长，但其实对于某一种支付类型来说，很多字段并没有用，而且对于后续维护都带来了困难，那我们就需要对入参实体按不同的支付类型进行拆分；

## 实体拆分

拆分简单，对于接口通用字段抽象到父类，不同支付类型为子类即可，简单示例如下：

1. 父类：
  1. @Data
public class BasePayEntity {
  /**
   * 机构编码
   */
  private String orgCode;
  /**
   * appid
   */
  private String appId;
  /**
   * app密钥
   */
  private String appSecret;
  /**
   * 支付方式
   */
  private String payType;
}
2. @Data
public class BasePayEntity {
  /**
   * 机构编码
   */
  private String orgCode;
  /**
   * appid
   */
  private String appId;
  /**
   * app密钥
   */
  private String appSecret;
  /**
   * 支付方式
   */
  private String payType;
}
3. 微信子类：
  1. @EqualsAndHashCode(callSuper = true)
@Data
public class WXPayEntity extends BasePayEntity {

  /**
   * 通道商户号
   */
  private String channelBusinessNo;
  /**
   * 公众号APPID
   */
  private String publicAppId;
}
4. @EqualsAndHashCode(callSuper = true)
@Data
public class WXPayEntity extends BasePayEntity {

  /**
   * 通道商户号
   */
  private String channelBusinessNo;
  /**
   * 公众号APPID
   */
  private String publicAppId;
}
5. 支付宝子类：
  1. @EqualsAndHashCode(callSuper = true)
@Data
public class ZFBPayEntity extends BasePayEntity {

  /**
   * 商户私钥
   */
  private String privateKey;
  /**
   * APIv3密钥
   */
  private String secretKey;
}
6. @EqualsAndHashCode(callSuper = true)
@Data
public class ZFBPayEntity extends BasePayEntity {

  /**
   * 商户私钥
   */
  private String privateKey;
  /**
   * APIv3密钥
   */
  private String secretKey;
}


子类拆分完毕，但是API如何自动映射到对应的请求体呢？

1. 使用Object/String接收，在代码中再根据类型自行反序列化，但我们作为程序员，主打一个优雅，这种方式属实不够优雅。
2. 本文重点：多态反序列化


## 接口请求的多态反序列化

1. jackson的多态反序列化：主要使用两个注解实现：@JsonTypeInfo 和 @JsonSubTypes，对于这两个注解的详细介绍：[Jackson对多态和多子类序列化的处理配置 - toto怎么会喝醉 - 博客园 (cnblogs.com)](https://www.cnblogs.com/lknny/p/5757784.html)，[Jackson里使用@JsonTypeInfo注解处理多态类型的序列化和反序列化_jsontypeinfo注解用法-CSDN博客](https://blog.csdn.net/u010979642/article/details/110524277)
2. 简单示例：
  1. 定义一个统一入参实体：
    1. @Data
public class PayRequest<T extends BasePayEntity> {

  private String payType;

  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXTERNAL_PROPERTY, property = "payType")
  private T payInfo;
}
  2. @Data
public class PayRequest<T extends BasePayEntity> {

  private String payType;

  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXTERNAL_PROPERTY, property = "payType")
  private T payInfo;
}
  3. 父类BasePayEntity添加@JsonSubTypes注解，如下：
    1. @Data
@JsonSubTypes(
    {
        @JsonSubTypes.Type(value = WXPayEntity.class, name = "WX"),
        @JsonSubTypes.Type(value = ZFBPayEntity.class, name = "ZFB")
    }
)
public class BasePayEntity {
  /**
   * 机构id
   */
  private String orgCode;
  /**
   * 小程序appid
   */
  private String appId;
  /**
   * 公众号密钥
   */
  private String appSecret;
}
  4. @Data
@JsonSubTypes(
    {
        @JsonSubTypes.Type(value = WXPayEntity.class, name = "WX"),
        @JsonSubTypes.Type(value = ZFBPayEntity.class, name = "ZFB")
    }
)
public class BasePayEntity {
  /**
   * 机构id
   */
  private String orgCode;
  /**
   * 小程序appid
   */
  private String appId;
  /**
   * 公众号密钥
   */
  private String appSecret;
}
  5. 其余子类无变化
  6. API示例：
    1. @PostMapping("/add")
  public <T extends BasePayEntity> void pay(@RequestBody PayRequest<T> request)
      throws JsonProcessingException {
    System.out.println(objectMapper.writeValueAsString(request));
  }
  7. @PostMapping("/add")
  public <T extends BasePayEntity> void pay(@RequestBody PayRequest<T> request)
      throws JsonProcessingException {
    System.out.println(objectMapper.writeValueAsString(request));
  }
  8. 示例演示：
    1. 
  9. 
  1. @Data
public class PayRequest<T extends BasePayEntity> {

  private String payType;

  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXTERNAL_PROPERTY, property = "payType")
  private T payInfo;
}
  1. @Data
@JsonSubTypes(
    {
        @JsonSubTypes.Type(value = WXPayEntity.class, name = "WX"),
        @JsonSubTypes.Type(value = ZFBPayEntity.class, name = "ZFB")
    }
)
public class BasePayEntity {
  /**
   * 机构id
   */
  private String orgCode;
  /**
   * 小程序appid
   */
  private String appId;
  /**
   * 公众号密钥
   */
  private String appSecret;
}
  1. @PostMapping("/add")
  public <T extends BasePayEntity> void pay(@RequestBody PayRequest<T> request)
      throws JsonProcessingException {
    System.out.println(objectMapper.writeValueAsString(request));
  }
  1. 
3. 定义一个统一入参实体：
  1. @Data
public class PayRequest<T extends BasePayEntity> {

  private String payType;

  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXTERNAL_PROPERTY, property = "payType")
  private T payInfo;
}
4. @Data
public class PayRequest<T extends BasePayEntity> {

  private String payType;

  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXTERNAL_PROPERTY, property = "payType")
  private T payInfo;
}
5. 父类BasePayEntity添加@JsonSubTypes注解，如下：
  1. @Data
@JsonSubTypes(
    {
        @JsonSubTypes.Type(value = WXPayEntity.class, name = "WX"),
        @JsonSubTypes.Type(value = ZFBPayEntity.class, name = "ZFB")
    }
)
public class BasePayEntity {
  /**
   * 机构id
   */
  private String orgCode;
  /**
   * 小程序appid
   */
  private String appId;
  /**
   * 公众号密钥
   */
  private String appSecret;
}
6. @Data
@JsonSubTypes(
    {
        @JsonSubTypes.Type(value = WXPayEntity.class, name = "WX"),
        @JsonSubTypes.Type(value = ZFBPayEntity.class, name = "ZFB")
    }
)
public class BasePayEntity {
  /**
   * 机构id
   */
  private String orgCode;
  /**
   * 小程序appid
   */
  private String appId;
  /**
   * 公众号密钥
   */
  private String appSecret;
}
7. 其余子类无变化
8. API示例：
  1. @PostMapping("/add")
  public <T extends BasePayEntity> void pay(@RequestBody PayRequest<T> request)
      throws JsonProcessingException {
    System.out.println(objectMapper.writeValueAsString(request));
  }
9. @PostMapping("/add")
  public <T extends BasePayEntity> void pay(@RequestBody PayRequest<T> request)
      throws JsonProcessingException {
    System.out.println(objectMapper.writeValueAsString(request));
  }
10. 示例演示：
  1. 
11.