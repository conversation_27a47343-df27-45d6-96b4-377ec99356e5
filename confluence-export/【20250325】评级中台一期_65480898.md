# 【20250325】评级中台一期

# 一、业务背景

## 1.1 需求背景

1. 顾客服务部有诉求征询顾客的门店服务评价，当前为信息中心定制的门店服务评价H5页面，需要迁移到一心到家作为评价入口；
2. 一心助手增加门店服务评价二维码下载操作；
3. 当前各业务线中有评价需求，需将评价中台化管理，用于评价模板自定义设置、评价风险管理，抽象评价数据反馈业务决策等功能。


## 1.2 痛点分析

1. 需将评价中台化管理


## 1.3 系统现状

1. 只存在为信息中心定制的门店服务评价H5页面


# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

1. 将当前门店服务评价表单定制开发上架到一心到家小程序
2. 提供单个、批量门店下载二维码功能给到应用端
3. 构建评价中台一期，包含评价模板，评价内容管理两个模块


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

## 4.3 时序图

# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.2.1 前端交互接口

### 5.2.2 对外接口

## 5.3 涉及数据库

#### 5.3.1 评价模板表

sqlDROP TABLE IF EXISTS evaluation_template;
CREATE TABLE evaluation_template(
`id` INT AUTO_INCREMENT COMMENT 'id' ,
`template_id` VARCHAR(255) NOT NULL COMMENT '模版ID' ,
`name` VARCHAR(20) COMMENT '模版名称' ,
`type` VARCHAR(20) COMMENT '模版类型（STORE-门店,COMMODITY-商品,ORDER-订单）' ,
`title` VARCHAR(255) COMMENT '模版标题' ,
`description` VARCHAR(255) COMMENT '模版描述' ,
`person_type` VARCHAR(30) COMMENT '评价人类型(MEMBER-会员,NONMEMBER-非会员,ALL-所有人))' ,
`status` VARCHAR(20) COMMENT '模版状态(OPEN-开启,CLOSE-关闭)' ,
`version` BIGINT COMMENT '数据版本，每次update+1' ,
`created_by` VARCHAR(50) COMMENT '创建人' ,
`created` DATETIME COMMENT '平台创建时间' ,
`updated_by` VARCHAR(50) COMMENT '更新人' ,
`updated` DATETIME COMMENT '平台更新时间' ,
`sys_create_time` DATETIME COMMENT '系统创建时间' ,
`sys_update_time` DATETIME COMMENT '系统更新时间' ,
PRIMARY KEY (id)
) COMMENT = '评价模板表' collate = utf8mb4_general_ci
  row_format = DYNAMIC;

create index idx_template_id_name
    on evaluation_template(template_id,name);  

#### 5.3.2 评价问题表

sqlDROP TABLE IF EXISTS evaluation_question;
CREATE TABLE evaluation_question(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `question_id` BIGINT NOT NULL  COMMENT '问题ID' ,
    `template_id` VARCHAR(50) NOT NULL  COMMENT '模版ID' ,
    `question_text` VARCHAR(1000) NOT NULL  COMMENT '问题内容' ,
    `question_type` VARCHAR(30) NOT NULL  COMMENT '问题类型(SINGLE_CHOICE-单选题，MULTIPLE_CHOICE-多选题，TEXT-文字)' ,
    `is_required` TINYINT(255)   COMMENT '是否必答(0-否,1-是)' ,
    `display_order` VARCHAR(255)   COMMENT '显示顺序' ,
    `options` JSON   COMMENT '选项配置(适用于选择题)' ,
    `version` BIGINT   COMMENT '数据版本，每次update+1' ,
    `created_by` VARCHAR(50)   COMMENT '创建人' ,
    `created` DATETIME   COMMENT '平台创建时间' ,
    `updated_by` VARCHAR(50)   COMMENT '更新人' ,
    `updated` DATETIME   COMMENT '更新时间' ,
    `sys_create_time` VARCHAR(255)   COMMENT '系统创建时间' ,
    `sys_update_time` VARCHAR(255)   COMMENT '系统更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '评价问题表' collate = utf8mb4_general_ci
  row_format = DYNAMIC; 

 create index idx_question_id
    on evaluation_question(question_id);  

  create index idx_template_id
    on evaluation_question(template_id);   

#### 5.3.2 评价关联表

sqlDROP TABLE IF EXISTS evaluation_relation;
CREATE TABLE evaluation_relation(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `evaluation_id` VARCHAR(50) NOT NULL  COMMENT '评价ID' ,
    `template_id` VARCHAR(255)   COMMENT '模板ID' ,
    `evaluation_type` VARCHAR(20)   COMMENT '评价类型(STORE-门店,COMMODITY-商品,ORDER-订单)' ,
    `organization_code` VARCHAR(20)   COMMENT '所属门店' ,
    `source_channel` VARCHAR(20)   COMMENT '评价来源渠道(APPLET-小程序)' ,
    `user_open_id` VARCHAR(255)   COMMENT '用户ID' ,
    `user_type` VARCHAR(255)   COMMENT '用户类型(MEMBER-会员,NONMEMBER-非会员)' ,
    `status` VARCHAR(20)   COMMENT '评价状态（DELETED-已删除,PUBLISHED-已发布,WAIT_PUBLISH-待发布）;默认待发布' ,
    `evaluate_time` DATETIME   COMMENT '评价时间' ,
    `extend_json` VARCHAR(255)   COMMENT '拓展字段' ,
    `version` BIGINT(32)   COMMENT '数据版本，每次update+1' ,
    `created` DATE   COMMENT '平台创建时间' ,
    `created_by` VARCHAR(50)   COMMENT '创建人' ,
    `updated` DATETIME   COMMENT '平台更新时间' ,
    `sys_create_time` DATETIME   COMMENT '系统创建时间' ,
    `sys_update_time` DATETIME   COMMENT '系统更新时间' ,
    PRIMARY KEY (id)
 constraint uk_template_organization_user_created
        unique (template_id, organization_code,user_open_id,created)
)  COMMENT = '评价用户表';

create index idx_template_id
   on evaluation_relation(template_id);  

 create index idx_evaluation_id
   on evaluation_relation(evaluation_id);   

  create index idx_organization_code
   on evaluation_relation(organization_code);   

#### 5.3.3 评价回答表

sqlDROP TABLE IF EXISTS evaluation_response;
CREATE TABLE evaluation_response(
    `id` INT AUTO_INCREMENT COMMENT 'id' ,
    `question_id` BIGINT   COMMENT '关联问题ID' ,
    `evaluation_id` VARCHAR(255) NOT NULL  COMMENT '评价ID' ,
    `choice_value` VARCHAR(100)   COMMENT '选项值(用于选项题)' ,
    `text_value` TEXT   COMMENT '文本回答(用于开放题)' ,
    `order_id` VARCHAR(255)   COMMENT '关联订单ID' ,
    `commodity_id` VARCHAR(255)   COMMENT '关联商品ID' ,
    `version` VARCHAR(32)   COMMENT '数据版本，每次update+1' ,
    `created_by` VARCHAR(32)   COMMENT '创建人' ,
    `created` DATETIME   COMMENT '平台创建时间' ,
    `updated_by` VARCHAR(32)   COMMENT '更新人' ,
    `updated` DATETIME   COMMENT '平台更新时间' ,
    `sys_create_time` VARCHAR(255)   COMMENT '系统创建时间' ,
    `sys_update_time` VARCHAR(255)   COMMENT '系统更新时间' ,
    PRIMARY KEY (id)
)  COMMENT = '评价回答表';

  create index evaluation_id
   on evaluation_response(evaluation_id); 



#### 5.3.3 评价统计表

## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.6 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2025年4月3日;**

**研发时间：2025年4月7日-2025年4月18日**

**测试时间：**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等