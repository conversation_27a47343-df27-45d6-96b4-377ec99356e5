# kafka扩分区&调整canal配置

### 背景

在批量变更DB数据时只有一个分区一个线程在消费kafka消息,效率低



![now-order-kakfa-consumer](./resources/now-order-kakfa-consumer.png)



### 调整

canal

# 配置分区数。如果不配配置,默认到一个分区
canal.mq.partitionsNum=3
# 配置路由hash
canal.mq.partitionHash=dscloud_offline\\.offline_order.*:order_no,dscloud_offline\\.offline_refund_order.*:refund_no

### 计划

| Topic | 扩分区 |  |
| --- | --- | --- |
| order_member_transaction_record_topic | 32 | Green已完成 |




![alsdjfowe](./resources/alsdjfowe.png)

