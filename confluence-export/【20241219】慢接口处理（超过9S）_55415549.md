# 【20241219】慢接口处理（超过9S）

一.慢接口统计(近一周的数据统计)

性能监控大盘 - Elastic

| 服务 | 慢接口 | 超时次数 | 是否需要优化 | 负责人 |  |
| --- | --- | --- | --- | --- | --- |
| hydee-business-order | /businesses-gateway/dscloud/2.0/ds/order/pick/confirm | 578 | 需要 |  |  |


**二.处理流程**

1.通过elk监测近一周的慢接口（超过9S）
2.通过[链路追踪-skywalking](https://skywalking.hxyxt.com/General-Service/Services)找到对应的服务的指定接口

3.选择【Trace Segments】里面报红的数据，通过对应的树结构和表结构，找到最慢的点，如

4.找到对应代码，做对应处理

**三.慢接口处理**

| 慢接口 | 处理方式 |
| --- | --- |
| /businesses-gateway/dscloud/2.0/ds/order/pick/confirm | 1.hydee-middle-data-sync /inner/hana/erp/getNoStock/v3 处理结果：接口调用时长12.755S，已联系商品同学处理2.com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:123) at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:955) at com.mysql.cj.jdbc.ClientPreparedStatement.sw$original$execute$vllu0t2(ClientPreparedStatement.java:372) at com.mysql.cj.jdbc.ClientPreparedStatement.sw$original$execute$vllu0t2$accessor$sw$2q6k991(ClientPreparedStatement.java) at com.mysql.cj.jdbc.ClientPreparedStatement$sw$auxiliary$7r6o192.call(Unknown Source) at org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstMethodsInter.intercept(InstMethodsInter.java:86) at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java) at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461) at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:627) at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459) at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440) at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459) at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167) at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497) at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47) at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74) at sun.reflect.GeneratedMethodAccessor366.invoke(Unknown Source) at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) at java.lang.reflect.Method.invoke(Method.java:498) at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) at com.sun.proxy.$Proxy719.update(Unknown Source) at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54) at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184) at sun.reflect.GeneratedMethodAccessor449.invoke(Unknown Source) at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) at java.lang.reflect.Method.invoke(Method.java:498) at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) at com.sun.proxy.$Proxy241.insert(Unknown Source) at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:278) at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:58) at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:62) at com.sun.proxy.$Proxy320.insertBatch(Unknown Source) at sun.reflect.GeneratedMethodAccessor4439.invoke(Unknown Source) at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) at java.lang.reflect.Method.invoke(Method.java:498) at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198) at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139) at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)sql：INSERT INTO `order_pick_info` ( `order_detail_id`, `erp_code`, `commodity_batch_no`, `count`, `purchase_price` ) values (?,?,?,?,?)，参数：[1222883137,146893,CA2N,1,NULL]处理结果：暂未复现，通过查询资料，总结如下：order_pick_info存在索引覆盖了order_detail_id列，sql语句[update order_pick_info set is_valid = 0 where order_detail_id in (select id from order_detail where order_no = #{orderNo})]，当where条件不匹配任何行时，InnoDB 会锁定索引中的间隙，以防止其他事务在这段时间内插入新的 order_detail_id；把上述sql进行拆解，先进行查询，如果有则更新，无则为跳过，借鉴资料：[mysql数据库先update后insert死锁分析](https://blog.51cto.com/u_15101587/2623375) |