# 1.订单下账问题

下账原因分类

- O2O
  - 库存不足
    - 修改批次号 → 需要人工介入，换成有库存的批号
    - 可以推荐批次号→ 用户输入/选择 完之后，调用接口
  - 修改批次号 → 需要人工介入，换成有库存的批号
  - 可以推荐批次号→ 用户输入/选择 完之后，调用接口
  - 金额问题
    - 改数据库 → 可以写接口
      - 负数情况
      - 精度问题
    - 负数情况
    - 精度问题
    - 负数情况
    - 精度问题
  - 改数据库 → 可以写接口
    - 负数情况
    - 精度问题
  - 负数情况
  - 精度问题
  - 追溯码问题
    - 海典那边需要维护追溯码数据。
    - 清理追溯码表数据，无追溯码下账
  - 海典那边需要维护追溯码数据。
  - 清理追溯码表数据，无追溯码下账
  - 修改批次号 → 需要人工介入，换成有库存的批号
  - 可以推荐批次号→ 用户输入/选择 完之后，调用接口
  - 改数据库 → 可以写接口
    - 负数情况
    - 精度问题
  - 负数情况
  - 精度问题
  - 负数情况
  - 精度问题
  - 海典那边需要维护追溯码数据。
  - 清理追溯码表数据，无追溯码下账
- 库存不足
  - 修改批次号 → 需要人工介入，换成有库存的批号
  - 可以推荐批次号→ 用户输入/选择 完之后，调用接口
- 修改批次号 → 需要人工介入，换成有库存的批号
- 可以推荐批次号→ 用户输入/选择 完之后，调用接口
- 金额问题
  - 改数据库 → 可以写接口
    - 负数情况
    - 精度问题
  - 负数情况
  - 精度问题
  - 负数情况
  - 精度问题
- 改数据库 → 可以写接口
  - 负数情况
  - 精度问题
- 负数情况
- 精度问题
- 追溯码问题
  - 海典那边需要维护追溯码数据。
  - 清理追溯码表数据，无追溯码下账
- 海典那边需要维护追溯码数据。
- 清理追溯码表数据，无追溯码下账
- B2C


都在一个chatflow里面做。意图识别，问题分类，处理

Dify:

1. 先判断订单类型,切入到上下文