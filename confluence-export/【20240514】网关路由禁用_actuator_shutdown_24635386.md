# 【20240514】网关路由禁用/actuator/shutdown

分支: release-forbidden-actuator-shutdown

RouteForbiddenFilter :

java@Component
public class RouteForbiddenFilter implements GlobalFilter, Ordered {

  @Resource
  private RouteForbiddenConfig routeForbiddenConfig;

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    String path = exchange.getRequest().getURI().getPath();
    if (StringUtils.isNotEmpty(path) && routeForbiddenConfig.containUrl(path)) {
      exchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
      return exchange.getResponse().setComplete();
    }
    return chain.filter(exchange);
  }

  /**
   * 得到权重，order越小，优先级越高
   *
   * @return int
   */
  @Override
  public int getOrder() {
    return Integer.MIN_VALUE;
  }

}

配置类:

java@SuppressWarnings({"unused", "SpringFacetCodeInspection"})
@Configuration
@ConfigurationProperties("route-forbidden-config")
@Data
public class RouteForbiddenConfig {

    private List<String> urlList;

    public Boolean containUrl(String urlPath) {
        if (CollectionUtils.isEmpty(this.urlList)) {
            return Boolean.FALSE;
        }
        for (String url : this.urlList) {
            if (urlPath.contains(url)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}



配置文件:

> 不配置,则不生效

ymlroute-forbidden-config:
  urlList:
    - /actuator/shutdown

最终结果:

1. 通过网关不能关闭下游微服务
2. 网关自身的shutdown不受影响