# 空接口自动生成mock数据

# 1. 背景

在开发新需求时,往往前后端是并行开发, 后端会先将接口定义写好Controller层,并通过swagger描述接口定义,并发布到开发环境. 测试同学通过文档定义进行开发. 前后端都开发完成后,会进行联调工作,这时前端同学会调用后端接口查看请求参数是否正确,响应参数是否正确设置到页面上.

在后端没有开发完成时, 只是通过定义就进行开发前端同学往往要自己mock数据查看页面显示,非常不方便,经常听到前端同学催促后端提供mock数据进行测试的情况. 

# 2. 原因分析

现在公司没有一个工具或服务对后端接口进行自动mock的工具. 虽然客户端上 PostMan ,ApiFox 都提供虚拟mock能力, 每个同学的对这些工具的了解成都也不一样, 没有形成一个统一的方案来解决这个事情. 

如果有一个统一的mock服务提供,无需配置,直接使用,能提高大家的联调效率

# 3. 实现方案

## 3.1 开源方案 easy mock

[https://mock.mengxuegu.com/login](https://mock.mengxuegu.com/login) 账号jackson 密码jackson

[https://mock.mengxuegu.com/docs](https://mock.mengxuegu.com/docs)

[https://zhuanlan.zhihu.com/p/11661058460](https://zhuanlan.zhihu.com/p/11661058460)

如果只是mock数据, easy mock就已经完全够用了,唯一缺点就是生产的内容需要进行约束定义.

## 3.2 mock服务 + ai 生成能力

之前开发的mock服务提供mock能力,通过swagger-api 接口([http://10.10.103.113:18000/report/v2/api-docs](http://10.10.103.113:18000/report/v2/api-docs)) 获取定义,拆解后投喂给ai,让其生成mock数据.

## 3.3 方案对比

个人觉得 easy mock方案性价比最高, 各大厂商都在使用

| 方案 | 研发成本 | mock数据效果 | 编辑mock数据能力 | 同步swagger |
| --- | --- | --- | --- | --- |
| easy mock | 无需研发,直接使用 | 无规则字符串和数字 | 有,可以编辑生成规则 | 手动 |
| mock server + ai | 需要对mock-server进行开发,扩展其能力 | ai很强,根据接口描述和字段都能生成很好的数据 | 无 | 无 |


# 4. 接入方案

- 直接修改请求地址host到mock服务地址
- 通过网关的动态路由能力,将需要路由的接口添加请求头就能转发到mock服务