# 【20240118】超时配置上线

整理链接: 

为了避免不可控的情况发生(eg. Pod重启)并尽量降低影响,配置修改计划跟着项目发布走,不提前修改配置

#### MySQL调整

ymlurl调整:

&connectTimeout=2000&socketTimeout=60000

druid:
    initialSize: 40
    minIdle: 40
    maxActive: 100
    maxWait: 10000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000

---
            max-active: 100
            initial-size: 40
            min-idle: 40
            max-wait: 10000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
---
spring.datasource.druid.max-active = 100
spring.datasource.druid.initial-size = 40
spring.datasource.druid.min-idle = 40
spring.datasource.druid.max-wait = 10000
spring.datasource.druid.time-between-eviction-runs-millis = 60000
spring.datasource.druid.min-evictable-idle-time-millis = 300000



ES调整: 10s

feign默认

feign:
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 15000

其中有些项目不含mysql配置，如果涉及到这些项目的发版,会再次复核

| 项目 | 开发环境验证 | MySQL生产配置调整 | MySQL调整 Redeploy | 上线后观察应用 | 备注 |  | Feign默认配置 | Feign Redeploy |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| hydee-middle-order | 110 incomplete | 111 complete     247 complete | 274 complete | 112 complete |  |  |  | 302 complete |
| hydee-business-order | 170 incomplete | 171 complete     248 complete | 275 complete | 276 complete |  |  | 303 complete | 304 complete |
| hydee-business-order-lua | 173 incomplete | 174 complete  无需更新   249 complete |  |  | 在apollo上无此配置 |  |  |  |
| hydee-business-order-ext | 176 incomplete | 177 complete     250 complete | 277 incomplete  未找到改项目 | 178 incomplete |  |  |  |  |
| hydee-business-order-web | 179 incomplete | 180 complete     251 complete | 278 complete | 181 complete |  |  |  |  |
| hydee-business-order-b2c-third | 182 incomplete | 183 complete     252 complete | 286 complete | 184 complete |  |  |  | 305 complete |
| hydee-third-inside | 185 incomplete | 186 complete 无需变更   253 complete |  |  | 在apollo上无此配置 |  |  |  |
| middle-datasync-message | 188 incomplete | 189 complete     254 complete | 287 complete | 190 complete |  |  |  |  |
| hydee-middle-sdp | 191 incomplete | 192 complete     255 complete | 288 complete | 193 complete |  |  |  | 306 complete |
| hydee-print | 194 incomplete | 195 complete  无需变更 |  |  | 在apollo上无此配置 |  |  |  |
| the3platform-message | 197 incomplete | 198 complete  无需变更 |  |  |  |  |  |  |
| the3platform-adapter | 200 incomplete | 201 complete     256 complete | 279 complete | 202 complete |  |  |  | 307 complete |
| hecms_aurora | 203 incomplete | 204 complete 无需变更    257 complete |  |  | .net项目 |  |  |  |
| hydee-middle-payment | 206 incomplete | 207 complete     258 complete | 280 complete | 208 complete |  |  | 308 complete | 309 complete |
| h3-pay-core | 209 incomplete | 210 complete     259 complete | 281 complete | 211 complete |  |  | 无feign配置 |  |
| h3-pay-finance | 212 incomplete | 213 complete     260 complete | 282 complete | 214 complete |  |  | 无feign配置 |  |
| businesses-gateway | 215 incomplete | 216 complete  无需配置   261 complete |  |  |  |  | 310 complete | 311 complete |
| hydee-xxl-job | 218 incomplete | 219 complete  无需配置   262 complete |  |  |  |  | 312 complete | 316 complete |
| middle-id | 221 incomplete | 222 complete 无需配置   263 complete |  |  |  |  | 无feign配置 |  |
| hydee-api-gateway | 224 incomplete | 228 complete 无需配置   264 complete |  |  |  |  | 313 complete | 314 complete |