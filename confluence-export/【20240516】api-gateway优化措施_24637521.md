# 【20240516】api-gateway优化措施

### 背景

下游服务慢SQL导致api-gateway网关重启

### 优化措施

|  | 措施 | 是否完成 | 备注 |
| --- | --- | --- | --- |
| 1 | 重写health接口 | 6 complete 已上线 | 需要验证使用重写的health接口，当接口可以访问时是否会出现下游服务调用失败的情况 |
| 2 | 分流, 新起一个项目，将占比较大的pos 流量分出去 | 9 complete  已完成 | 新的service名 |
| 3 | 灾备 |  |  |
| 4 | 调整pod配置；扩容 | 7 complete k8s健康检查时间调整为30s(截止20240517评价启动时间为22s左右) 已上线 | 临时扩容2个节点 |
| 5 | 网关大版本升级 |  | 需要升级到netty版本 |
| 6 | 内网调用 | 暂不采用 | 已经确认在华为云上，内网是打通的 |


### 分支

api-gateway release-optimize-20240517



### 重定向健康检查接口

management:
  endpoints:
    web:
      exposure:
        include: ["*"]
      path-mapping:
        health: /actuator/health # 使用mapping

### 查看处理线程数

开发环境



4个线程处理

curl localhost:9404/actuator/api-gateway-info

用处理器数量4,ioWorkerCount:null,ioSelectCount:null

生产环境是8个线程在处理:

参考资料[https://www.cnblogs.com/myshare/p/16616880.html](https://www.cnblogs.com/myshare/p/16616880.html)发现不配置ReactorNetty.IO_SELECT_COUNT则无selector thread

api-gateway添加自定义的参数数值

api:
  netty:
    enable: true # 开启时下面配置则生效
    ioWorkerCount: 8 # dev环境可用核数的2倍
    ioSelectCount: 1 

调整后生效,ioWorkerCount个工作线程,ioSelectCount个Select线程

压过一次，没有明显提升.....待继续验证