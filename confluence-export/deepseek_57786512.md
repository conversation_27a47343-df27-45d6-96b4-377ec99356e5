# deepseek

[https://chat.deepseek.com/](https://chat.deepseek.com/)

官网:[https://www.deepseek.com/](https://www.deepseek.com/)

基于pake开源项目构建了一个windows的包,可以安装使用:

*【这是网上对DeepSeek模型的一个评价。】*

DeepSeek R1 的论文看完后，后劲很大。虽然我推荐所有人都去阅读一下，但我估计实际去读的人应该很少。今天把论文里的三个亮点，用通俗易懂地方式写出来，希望能让更多人了解这篇论文有多么重要。

### 亮点一： 告别“刷题班”，纯“实战”也能练出推理大神！

我们平时学习，是不是经常要“刷题”？ 做大量的练习题，才能巩固知识，提高解题能力。 以前训练AI模型，也差不多是这个套路，要先给AI“喂”大量的“习题”（监督数据），让它学习知识和语言，然后再进行“特训”（微调），提升特定技能。

这种“刷题+特训”的模式，好像已经成了AI界的“标准操作”。

但是，DeepSeek-AI团队却偏偏不走寻常路，他们想试试看：能不能让AI跳过“刷题班”，直接通过“实战演练”（强化学习）来提升推理能力？

他们就搞出了一个叫做 DeepSeek-R1-Zero 的模型，这个模型最牛的地方在于，它完全没有“刷题”，直接就上了“战场”——用强化学习（RL）技术，对基础模型进行训练。

这就像啥感觉呢？ 就好比我们训练一个篮球队员，不是先让他背各种篮球战术和技巧，而是直接把他放到球场上，让他在比赛中不断尝试、不断摸索、不断进步！

结果你猜怎么着？ 这种看似“野蛮”的训练方式，竟然也培养出了推理能力超强的AI模型！ DeepSeek-R1-Zero 在各种推理能力测试中表现惊艳，甚至还展现出一些意想不到的“超能力”：

**“自我验算”技能 (Self-Verification)**： 模型自己做完题后，还会“回头检查”，看看答案对不对，如果发现错了，还会自己改正！ 这简直就像考试时，学霸做完题还会认真验算一样，太自觉了！

**“反思总结”技能 (Reflection)**： 模型还能“反思”自己的思考过程，分析哪里做得好，哪里做得不好，简直就是“学而时习之”的AI版！

**“超长解题思路” (Long CoT)**： 模型能够生成非常详细的解题步骤，一步一步地展示它是怎么思考的，这就像学霸考试时，不仅写出答案，还把详细的解题过程都写出来，让你一看就明白！

更厉害的是，DeepSeek-R1-Zero 的这些推理能力，都是纯粹通过强化学习“自己长出来”的，没有借助任何“刷题”数据的帮助。 这就像在证明，即使不“刷题”，只要方法对头，“野路子”也能练成武林高手！

DeepSeek-R1-Zero 的成功，对于AI研究来说，简直是个重磅炸弹！ 它首次证明了，AI的推理能力，真的可以通过强化学习来“激发”出来，不需要死板地“刷题”。 这为我们打开了新的思路，原来训练AI，还可以这么“放飞自我”！

### 亮点二： “冷启动”+多阶段训练，打造更强推理“发动机” DeepSeek-R1

虽然 DeepSeek-R1-Zero 已经很厉害了，但DeepSeek-AI团队并不满足，他们还想更上一层楼，打造更强大的推理引擎！ 他们发现，R1-Zero 在实际应用中，还是有些小瑕疵，比如：

“看不懂的解题过程”： 模型有时候的推理过程，有点“跳跃”，不够直观，就像学霸的草稿纸，只有他自己能看懂。

“语言混乱”： 模型在处理一些复杂问题时，可能会出现“中英文混用”的情况，让人感觉有点“精分”。

为了解决这些问题，并进一步提升推理能力，DeepSeek-AI团队推出了 DeepSeek-R1 模型。 R1 模型在 R1-Zero 的基础上，进行了全面升级，秘诀就在于 “冷启动数据” 和 “多阶段训练”。

“冷启动数据”，就像是给模型一个“预习”，让它先对人类的推理方式有个初步了解。 研究人员收集了一些高质量的推理数据，先用这些数据对基础模型进行“热身”，让模型初步掌握人类期望的推理风格。

这就像什么呢？ 就好比运动员在正式训练前，要先做一些准备活动，拉伸筋骨，让身体进入状态，这样才能更好地适应高强度的训练。

“热身”之后，DeepSeek-R1 就进入了多阶段强化学习训练的“正赛”。 这个训练过程就像“升级打怪”，一步一个脚印，逐步提升模型的推理能力：

“推理能力专项提升” (Reasoning-oriented RL)： 在“热身”模型的基础上，进行强化学习训练，重点提升模型在数学、代码、逻辑推理等硬核任务上的能力，就像专门请了个“奥数金牌教练”来辅导模型一样。

“通用能力全面发展” (Rejection Sampling and Supervised Fine-Tuning)： 当模型在推理能力上取得显著进步后，利用强化学习模型的输出来生成新的高质量“习题”，并结合其他领域的“习题”（比如写作、问答等），再次进行“刷题”，全面提升模型的各种技能，就像让“奥数金牌选手”也去参加语数外全科竞赛，力争全面发展！

“用户体验优化” (Reinforcement Learning for all Scenarios)： 在模型“全科成绩”都提升之后，再进行第二阶段的强化学习训练，这次训练会考虑更广泛的场景和用户需求，让模型更“接地气”，更好用，更贴心，就像让“全能学霸”也去参加各种社会实践活动，提升综合素质，成为更受欢迎的人！

通过 “冷启动数据”+“多阶段训练” 的组合拳，DeepSeek-R1 模型不仅解决了R1-Zero 的一些小问题，还在推理能力上实现了 “火箭式” 提升。 实验结果表明，DeepSeek-R1 在各种推理任务上的表现，已经可以和 OpenAI 最顶尖的 o1-1217 模型 “掰手腕” 了！

### 亮点三： 推理能力“平民化”，小个子也能有大智慧！

大语言模型虽然很厉害，但动辄几百亿、上千亿的参数，就像个“巨无霸”，普通电脑根本跑不动，普通人也用不起。 怎么才能让推理能力“飞入寻常百姓家”，让大家都能享受到AI的智慧呢？ DeepSeek-AI 团队给出了一个妙招：知识蒸馏！

知识蒸馏，简单来说，就是把“大模型老师”的知识和能力，“压缩”到“小模型学生”身上。 DeepSeek-AI 团队以 “超级学霸” DeepSeek-R1 为 “老师”，训练出了一批 “迷你学霸”——小模型学生，包括 1.5B、7B、8B、14B、32B、70B 等多个版本。 （这里的“B”就是参数量的单位，数字越小，模型就越小）

更让人惊喜的是，这些 “迷你学霸” 表现超出了预期，不仅性能超过了同等大小的其他开源模型，甚至在某些方面，还能和一些更大的“闭源大牛”掰掰手腕！ 例如：

DeepSeek-R1-Distill-Qwen-7B （7B小模型）在 AIME 2024 测试中，成绩超过了 QwQ-32B-Preview （32B大模型）！ 这就像一个“小学生”打败了“大学生”，简直是“以下克上”的典范！

DeepSeek-R1-Distill-Qwen-32B （32B小模型） 在多个测试中，都取得了非常优秀的成绩，甚至可以媲美 OpenAI 的 o1-mini 模型 （也是个不小的模型）！ 这就像“迷你学霸”也能考出“重点高中”的水平，太励志了！

更更更重要的是，DeepSeek-AI 团队 免费开源 了 DeepSeek-R1-Zero、DeepSeek-R1，以及这六个 “迷你学霸” 模型！ 这意味着，我们这些普通人，也能免费用上这么强大的AI模型，简直是 “良心之作”！ 研究人员和开发者们也可以基于这些开源模型，进行更深入的研究和应用开发，共同推动AI技术的发展！

### 总结与展望

DeepSeek-R1 的出现，让我们看到了AI推理能力提升的更多可能性。 它不仅证明了纯强化学习路线的潜力，也为如何打造更强大、更实用、更亲民的AI模型，指明了新的方向。

总而言之，DeepSeek-R1 的问世，是AI发展史上一个重要的里程碑，它让我们看到了AI “思考” 的曙光，也让我们对未来的AI充满了期待！ 

希望这篇文章能让你对 DeepSeek-R1 有个初步的了解。 如果你对AI技术感兴趣，或者想了解更多DeepSeek-R1的细节，强烈建议你阅读一下论文原文，相信你会发现更多惊喜！