# 4.6.1 能力模型

**备注：应用研发组-后端研发岗能力模型，试运行版本。**

# 一、能力模型

| 维度 | 初级 | 中级 | 高级 |
| --- | --- | --- | --- |
| **角色定位****（Role Position）** | 【应用扩展，专业进阶】1. 负责开发模块或中小型产品以满足客户需求，是产品功能模块的主要代码贡献者。 2. 能够指导或帮助新同事工完成工作任务；复杂问题需要主管或高职级人员的指导。 | 【专业熟练、独当一面】1. 能独立设计、开发和改进系统或中型产品，是产品及系统的主要作者或维护者。 2. 能够辅导初级员工，帮助改进工作效率，提升能力发展。 | 【专业精深、攻坚克难】1. 独立负责难度较高的中、大型项目工作，或跨部门复杂项目中对专业性要求较强的关键模块。 2. 资深导师，能结合人员的不同特质和经历，采取不同的辅导策略，在指导过程中注重传授思维理念和工作技巧。 |
| **模糊度****（Ambiguity）** | 1. 问题明确，所负责模块的实现方式不明确。 2. 解决方案有优化空间，需要管理者/同事的辅导。 | 1. 技术策略明确，所负责的多模块或子系统的解决方案有待设计。 2. 应用专业知识来提出不同的解决方案建议，交付成果。 | 1. 业务问题与技术目标明确，系统的技术策略不明确。能够利用知识和技能探索目标的实现路径。 2. 能够设计出中长期解决方案，交付成果。 |
| **范围****（Scope）** | 1. 通常负责需求迭代和模块开发，可能会负责简单的常规或跨团队协作项目。 2. 需要管理者/同事的指导，与团队内其他成员协作开展工作。 | 1. 通常负责需求迭代和模块开发，能对**多模块或子系统**做出合理设计或选型。可能会负责常规的跨团队协作项目。 2. 基本可独立工作，部分复杂场景需接受技术辅导并开始辅导同事。 | 1. 通常负责**核心系统的架构设计**与系统开发，开始关注系统中长期技术规划。部分员工会负责重点产品或技术项目。 2. **通常跨团队协作，**部分员工开始指导/辅导同事，并评估团队员工产出。 |
| **影响****（Impact）** | 1. 可能影响产品/功能的客户体验、项目交付时间、代码质量等相关**团队指标**等。 2. 能够从工作中总结与提炼共性的规律，把岗位的工作心得或案例沉淀总结并输出成果，形成可复制的经验，提升工作效率。 | 1. 影响产品/功能的客户体验、代码质量和可维护性等**重要团队指标**。 2. 能够从工作中总结与提炼共性的规律，把岗位的**工作心得或案例沉淀总结并输出成果**，形成可复制的经验，提升工作效率。 3. 通过流程改进与知识分享，开始影响同伴如何工作。 | 1. 影响客户体验、系统架构等**重要业务、技术团队指标**。部分员工开始负责**团队目标**。 2. 主导开发部门内培训课程，部分员工开始参与团队招聘工作。 |
| **执行****（Execution）** | 1. 开发模块或中小型产品以满足客户需求，是产品功能模块的主要代码贡献者。 2. 与所在团队协作，共同完成目标。 | 1. 独立设计、开发和改进子系统或中型产品，是产品及子系统的主要作者或维护者。可简化流程或改进交付方式，使得子系统或产品的质量、效率大幅提升。 2. 与所在团队或团队间协作，共同完成目标。 | 1. 独立设计、开发和改进系统或中大型产品。能解决架构缺陷、简化和优化代码，让团队成员在建立的流程/规范下工作。是产品/系统的代码及架构设计的主要作者或维护者。 2. 通常主导团队内或跨团队项目，把控项目风险与进展，推动目标达成。 |
| **复杂度****（Complexity）** | 1. 能够负责常规需求迭代和模块开发，按时交付、保证质量。 2. 当目标与进展出现风险时，能够及时向上反馈。 | 1. 能够独立解决子系统级技术问题，在有限时间内高质量的完成系统级开发或子系统设计的交付。 2. 能够预估短期风险，提前协调解决。有能力处理紧急问题、判断是否升级、分析根因并推动解决。 3. 解决方案能够兼顾**质量和效率**。 | 1. 能够独立解决复杂困难的系统级架构及业务问题，致力于将复杂问题简单化、持续优化并消除瓶颈。 2. 能够识别和消除严重或长期风险。建立高效、系统性解决方案，解决大部分问题的根因。有能力统筹多团队协同处理线上问题。 3. 解决方案兼顾**可扩展性、可迁移性，**较好的平衡短期与长期收益。 |
| **知识****/****技能****（Knowledge/Skill）** | 1. 熟悉负责模块所提供的功能和服务，这些功能和服务涉及的一系列业务场景及要解决的问题，以及这些典型业务场景下涉及的角色、业务流程及核心关注点。 2. 熟练使用公司技术体系的语言/框架/工具/流程。了解最佳实践及常见缺陷，高质量完成模块级别代码开发及文档撰写。掌握初级的系统建模能力，能够对系统关键的接口、数据、流程等抽象建模。 | 1. 熟悉参与系统的关键产品特性、核心业务流程及领域知识。了解负责系统涉及领域的最佳实践、行业主流解决方案及产品。理解系统所服务的客户及其目标，对产品定位、目标客户、业务的发展以及分析等有一定的思考。 2. 熟练使用公司技术体系开发工具及方法，高质量完成系统级代码开发及文档撰写。能对多模块或子系统做出合理设计或选型，达成功能与非功能性（性能、安全、功能扩展）需求。熟悉各种技术场景的量化指标，达成优化目标。 | 1. 熟悉负责系统的关键产品特性、核心业务流程及领域知识。理解负责系统涉及领域的最佳实践、行业主流解决方案及产品，并合理应用。理解业务并参与定义关键业务度量指标、洞察影响关键指标的关键影响要素。 2. 熟悉高可扩展的系统架构设计工具或方法，具备独立负责完整系统架构设计能力。能够基于业务理解与系统现状，制定系统中长期技术规划，结合短期问题将架构设计落地。能够对负责系统建立技术评估指标，引入跨通道技术能力，解决复杂问题或大幅优化业务、技术指标。 |


# 二、绩效评估参考

为了体现员工绩效差异、保持团队活力，绩效结果划分为S、A、B、C四个等级，各等级标准、建议分值及分布比例如下：

| 评级 | 标准 | 建议分值 | **建议分布比例** |
| --- | --- | --- | --- |
| S | 卓越 | 业绩结果和过程行为显著超出当前岗位和职级期望。 | 90≤X≤100 | —— |
| A | 优秀 | 业绩结果和过程行为超出当前岗位和职级期望。 | 85≤X＜90 | —— |
| B | 达到或部分达到期望 | 业绩结果和过程行为达到或部分达到当前岗位和职级期望。 | 70≤X＜85 | —— |
| C | 未达到期望 | 业绩结果和过程行为未达到当前岗位和职级期望，存在明显差距，需改进。 | X＜70 | —— |


# 三、其他参考附录

| 维度 | 描述 |
| --- | --- |
| 成长潜力 | 1. **观察工作态度：**优秀的员工通常工作积极主动，勇于承担责任，对待工作充满热情。 2. **分析工作能力：**通过员工完成任务的速度、质量、创新思维等方面来评估其工作能力。具备高潜力的员工往往能够迅速学习新知识，适应新环境，并高效完成任务。 3. **沟通能力：**有效的沟通是职场成功的关键。留意员工在会议、报告以及与同事、客户沟通时的表现，评估他们的沟通能力。 4. **团队合作能力：**观察员工在团队项目中的表现，看他们是否能够与团队成员融洽合作，共同推动项目进展。 5. **解决问题的能力：**面对困难和挑战时，高潜力的员工通常能够迅速找到解决方案并付诸实践。注意员工在遇到困难时的应对策略。 6. **领导能力：**留意员工是否有领导潜质，如是否能带领和指导他人，是否能提出有建设性的建议和想法等。 7. **职业规划和发展意愿：**与员工交流，了解他们的职业规划和发展意愿。有明确职业规划和强烈发展意愿的员工通常更有潜力。 8. **培训和学习成果：**观察员工在培训和学习新技能后的表现，评估他们的学习能力和应用新知识的能力。 9. **反馈与评估：**定期与员工进行反馈和评估，了解他们的工作状况、遇到的挑战以及需要的支持。这也有助于你更准确地评估他们的潜力。 10. **保持开放心态：**每个员工都有独特的优点和潜力，不要过分关注员工的不足之处，而是以开放的心态看待他们的潜力和发展空间。 ---综上所述，评估员工潜力时需要综合考虑多个方面，包括工作态度、能力、沟通、合作、解决问题、领导能力等。同时，保持开放和长期的心态，给予员工充分的支持和培养，有助于更准确地发掘和发挥员工的潜力。 |
| 领导潜质 | 1. **观察员工的目标设定和追求：**注意员工是否能够设定明确、远大的个人和团队目标，并持续努力追求这些目标。优秀的领导者通常具备目标导向的思维，能够激励团队成员共同追求目标。 2. **考察员工的决策能力：**观察员工在面对问题和挑战时的决策过程。优秀的领导者通常能够全面考虑各种因素，做出明智且及时的决策，并能够为团队解释清楚决策背后的原因和逻辑。 3. **观察员工的团队合作能力：**注意员工在团队中的表现，是否愿意与他人合作，协调团队成员之间的关系，促进良好的工作氛围。优秀的领导者具备团队合作能力，能够有效地与不同背景、不同技能的人合作。 4. **评估员工的沟通能力：**优秀的领导者需要具备良好的沟通能力，能够清晰、准确地表达自己的意见和想法，倾听他人的观点，并给予反馈和建议。观察员工在会议、演讲和日常交流中的沟通能力。 5. **注意员工的领导能力：**留意员工是否有机会和倾向去指导和带领其他人。例如，他们是否经常主动承担责任，为团队提供方向和指导，并对团队成员的发展给予关注和支持。 6. **考察员工的解决问题的能力：**优秀的领导者通常能够主动发现问题，分析问题的根本原因，并提出解决方案。观察员工在面对困难和挑战时的态度和解决方法，评估他们的解决问题能力和应对压力的能力。 ---综上所述，评估员工的领导潜质是一个综合性的过程，需要观察员工在多个方面的表现和行为。同时，也要给予员工发展领导潜质的机会和支持，例如提供领导力培训、导师制度等。最重要的是，保持公正、开放的心态，避免主观偏见影响评估结果，真正发掘和培养潜力优秀的领导者。 |
| 岗位匹配度 | 1. **分析员工的技能和经验：**对比员工所拥有的技能和经验与岗位所要求的是否匹配。考察员工是否具备完成岗位任务所必需的专业知识和技术能力。 2. **观察员工的工作表现：**注意员工在岗位上的工作表现，包括任务完成情况、工作效率、工作质量等方面。员工是否能够胜任岗位的工作要求，并且取得良好的业绩？ 3. **岗位要求的符合程度：**评估员工是否能够完全遵守岗位的规章制度、工作要求和操作流程，以及是否能够准确理解和执行上级的指示和任务。 4. **员工的兴趣和职业规划：**了解员工对当前岗位的兴趣和满意度，以及他们的职业规划是否与岗位要求相符合。如果员工对岗位工作感兴趣并有明确的职业发展规划，通常更容易适应岗位要求。 5. **反馈与沟通：**定期与员工进行反馈和沟通，了解他们对岗位的看法和感受，以及他们是否面临任何困难或挑战。这有助于你更全面地评估员工的岗位匹配度，并及时采取必要的措施。 6. **团队合作与适应性：**评估员工在团队合作中的表现，是否与其他团队成员融洽合作，以及是否能够适应不断变化的工作环境和任务要求。岗位匹配度高的员工应能够与他人合作并在变化中保持高效工作。 ---综上所述，评估员工的岗位匹配度需要综合考虑员工的技能和经验、工作表现、符合岗位要求程度、兴趣和职业规划、反馈与沟通以及团队合作与适应性等方面。通过全面、客观地评估，你可以更好地了解员工是否适合当前岗位，从而做出相应的人事决策。 |
| 价值观 | **1.以客户为中心**-   - 客户需求是公司所有策略、行动的最重要的输入，帮客户解决问题，藉此创造价值，公司才有存在的理由。   - 所有直接或间接使用你的劳动成果的人，都是你的客户，包含外部客户与内部客户，只有把我们的客户服务好，才能不断创造客户，我们的团队和个人的存在才有价值。 - 客户需求是公司所有策略、行动的最重要的输入，帮客户解决问题，藉此创造价值，公司才有存在的理由。 - 所有直接或间接使用你的劳动成果的人，都是你的客户，包含外部客户与内部客户，只有把我们的客户服务好，才能不断创造客户，我们的团队和个人的存在才有价值。 **2.正直诚信**-   - 遵守国家法律与公司制度，不触犯企业高压线。   - 做人德为先，坚持诚实、守信等为人处事重要原则。   - 正直诚信不仅是敢于严格要求自己，更重要的是为客户坚守承诺，对同事言行一致。 - 遵守国家法律与公司制度，不触犯企业高压线。 - 做人德为先，坚持诚实、守信等为人处事重要原则。 - 正直诚信不仅是敢于严格要求自己，更重要的是为客户坚守承诺，对同事言行一致。 **3.合作共赢**-   - 团队内成员为了共同的目标，互相支持合作，不能只想自己利益，不管他人死活。   - 不论团队内外，以成就他人为荣，“胜则举杯相庆，败则拼死相救”。 - 团队内成员为了共同的目标，互相支持合作，不能只想自己利益，不管他人死活。 - 不论团队内外，以成就他人为荣，“胜则举杯相庆，败则拼死相救”。 **4.追求卓越**-   - 多渠道主动学习，并学以致用。   - 热爱自己的本职工作，主动接受困难与挑战，精益求精的工匠精神。   - 在工作中有前瞻思考，创造变化或革新并努力践行。   - 控制成本，有效提升效率，坚持追求最佳投入产出比。 - 多渠道主动学习，并学以致用。 - 热爱自己的本职工作，主动接受困难与挑战，精益求精的工匠精神。 - 在工作中有前瞻思考，创造变化或革新并努力践行。 - 控制成本，有效提升效率，坚持追求最佳投入产出比。 |