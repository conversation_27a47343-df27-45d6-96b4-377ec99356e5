# 【20240410】xxl-job 迁移

## 一、背景

 当前心云系统 xxl-job 任务调度中 执行器 ‘海典中台调度中心’，是通过 调用 [middle-hydee-xxl-job](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/pod/prod/middle-hydee-xxl-job-deployment-68646db849-jmnqq) 转发到对应的服务执行业务逻辑。

 这样做法有以下问题：

 1. xxl-job 的一些特性无法起效 比如 路由策略，配置为 第一个或最后一个，此时配置只是作用于 [middle-hydee-xxl-job](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/pod/prod/middle-hydee-xxl-job-deployment-68646db849-jmnqq)，并未作用到真正执行业务的服务。

 2. 链路过长，无论是开发还是排查问题增加了成本。

 3. 多个模块执行的任务都在同一个执行器下，导致执行器下任务过多，且任务命名混乱无层级关系。

 

![未命名绘图](./resources/未命名绘图_5.png)



## 二、目标

 1. 规范 xxl-job 执行器命名，任务命名

 2. 移除 执行器 ‘海典中台调度中心’， 停用 [middle-hydee-xxl-job](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/pod/prod/middle-hydee-xxl-job-deployment-68646db849-jmnqq) 模块。

## 三、执行

 1. 整理目前xxl-job配置的执行器和任务 

 [https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcUqzpLOE](https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcUqzpLOE)

 2. 由各团队将 执行器 ‘海典中台调度中心’ 归属于自己的任务迁移到对应模块

 2.1 目前整理的任务中有 40 条未知归属的任务，各团队划分出属于自己的任务，并修改状态

 2.2 各分组中可能存在不属于自己的任务可移动到未知分组中

 3. 执行器 ‘海典中台调度中心’ 任务移除完成且验证后，下线[middle-hydee-xxl-job](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/pod/prod/middle-hydee-xxl-job-deployment-68646db849-jmnqq) 服务

## 四、附录

 1. 

 2. 命名规范建议：

 **执行器命名： 组名-模块名 比如 订单-020中台、 订单-B2C中台**