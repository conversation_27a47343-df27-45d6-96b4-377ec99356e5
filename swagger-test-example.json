{"swagger": "2.0", "info": {"title": "测试API", "description": "用于测试参数类型解析的API"}, "host": "test.example.com", "basePath": "/", "paths": {"/test/api": {"post": {"tags": ["测试"], "summary": "测试接口", "parameters": [{"in": "body", "name": "req", "description": "请求参数", "required": true, "schema": {"$ref": "#/definitions/TestRequest"}}, {"in": "header", "name": "userId", "description": "用户ID", "required": true, "type": "string"}, {"in": "query", "name": "pageSize", "description": "页面大小", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/TestResponse"}}}}}}, "definitions": {"TestRequest": {"type": "object", "title": "测试请求", "required": ["name", "age"], "properties": {"name": {"type": "string", "description": "姓名"}, "age": {"type": "integer", "format": "int32", "description": "年龄"}, "email": {"type": "string", "description": "邮箱"}, "tags": {"type": "array", "description": "标签列表", "items": {"type": "string"}}, "profile": {"$ref": "#/definitions/UserProfile"}}}, "TestResponse": {"type": "object", "title": "测试响应", "properties": {"success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "description": "响应数据"}, "message": {"type": "string", "description": "响应消息"}}}, "UserProfile": {"type": "object", "title": "用户档案", "properties": {"avatar": {"type": "string", "description": "头像URL"}, "bio": {"type": "string", "description": "个人简介"}}}}}