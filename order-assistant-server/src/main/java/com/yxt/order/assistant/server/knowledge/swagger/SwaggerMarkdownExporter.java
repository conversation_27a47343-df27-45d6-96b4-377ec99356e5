package com.yxt.order.assistant.server.knowledge.swagger;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.assistant.server.config.OrderAssistantConfig;
import com.yxt.order.assistant.server.knowledge.swagger.dto.SwaggerDoc;
import com.yxt.order.assistant.server.repository.KnowledgeRepository;
import com.yxt.order.assistant.server.repository.entity.Knowledge;
import com.yxt.order.assistant.server.repository.enums.KnowledgeSource;
import com.yxt.order.assistant.server.repository.enums.KnowledgeTargetStatus;
import com.yxt.order.assistant.server.repository.enums.KnowledgeUploadStatus;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Swagger文档Markdown导出器
 */
@Slf4j
@Component
public class SwaggerMarkdownExporter {

    @Autowired
    private OrderAssistantConfig orderAssistantConfig;

    @Autowired
    private KnowledgeRepository knowledgeRepository;

    private OkHttpClient client;
    private Gson gson;
    private String outputDir;

    @PostConstruct
    public void init() {
        this.outputDir = "swagger-export";

        // 配置OkHttp客户端
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();

        // 配置Gson
        this.gson = new GsonBuilder().setPrettyPrinting().create();

        // 创建输出目录
        createOutputDirectories();
    }

    /**
     * 创建输出目录
     */
    private void createOutputDirectories() {
        File dir = new File(outputDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    /**
     * 导出Swagger文档为Markdown
     */
    public void exportSwaggerToMarkdown(Long knowledgeGroupId, String swaggerUrl) {
        log.info("🚀 开始导出Swagger文档，URL: {}", swaggerUrl);

        try {
            // 获取Swagger JSON数据
            String swaggerJson = fetchSwaggerJson(swaggerUrl);
            if (StringUtils.isEmpty(swaggerJson)) {
                log.warn("❌ 获取Swagger JSON数据失败");
                return;
            }

            // 解析JSON为对象
            SwaggerDoc swaggerDoc = gson.fromJson(swaggerJson, SwaggerDoc.class);
            if (swaggerDoc == null) {
                log.warn("❌ 解析Swagger JSON失败");
                return;
            }

            // 转换为Markdown
            String markdown = convertSwaggerToMarkdown(swaggerDoc);
            if (StringUtils.isEmpty(markdown)) {
                log.warn("❌ 转换Markdown失败");
                return;
            }

            // 保存到数据库
            saveToDatabase(knowledgeGroupId, swaggerDoc, markdown);

            // 保存到文件（如果配置允许）
            if (orderAssistantConfig.getCfPullWriteFile()) {
                saveToFile(swaggerDoc, markdown);
            }

            log.info("✅ Swagger文档导出完成");

        } catch (Exception e) {
            log.error("❌ 导出Swagger文档失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取Swagger JSON数据
     */
    private String fetchSwaggerJson(String swaggerUrl) {
        Request request = new Request.Builder()
                .url(swaggerUrl)
                .addHeader("Accept", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.warn("❌ 获取Swagger JSON失败: {} {}", response.code(), response.message());
                return null;
            }

            return response.body().string();
        } catch (Exception e) {
            log.error("❌ 请求Swagger URL失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换Swagger文档为Markdown
     */
    private String convertSwaggerToMarkdown(SwaggerDoc swaggerDoc) {
        StringBuilder markdown = new StringBuilder();

        // 添加标题和基本信息
        addBasicInfo(markdown, swaggerDoc);

        // 添加标签说明
        addTags(markdown, swaggerDoc);

        // 添加API接口
        addPaths(markdown, swaggerDoc);

        // 添加数据模型
        addDefinitions(markdown, swaggerDoc);

        return markdown.toString();
    }

    /**
     * 添加基本信息
     */
    private void addBasicInfo(StringBuilder markdown, SwaggerDoc swaggerDoc) {
        SwaggerDoc.Info info = swaggerDoc.getInfo();
        if (info != null) {
            markdown.append("# ").append(info.getTitle() != null ? info.getTitle() : "API文档").append("\n\n");
            
            if (!StringUtils.isEmpty(info.getDescription())) {
                markdown.append("## 描述\n\n");
                markdown.append(info.getDescription()).append("\n\n");
            }
        } else {
            markdown.append("# API文档\n\n");
        }

        // 添加服务器信息
        if (!StringUtils.isEmpty(swaggerDoc.getHost())) {
            markdown.append("## 服务器信息\n\n");
            markdown.append("- **主机**: ").append(swaggerDoc.getHost()).append("\n");
            if (!StringUtils.isEmpty(swaggerDoc.getBasePath())) {
                markdown.append("- **基础路径**: ").append(swaggerDoc.getBasePath()).append("\n");
            }
            markdown.append("\n");
        }
    }

    /**
     * 添加标签说明
     */
    private void addTags(StringBuilder markdown, SwaggerDoc swaggerDoc) {
        List<SwaggerDoc.Tag> tags = swaggerDoc.getTags();
        if (tags != null && !tags.isEmpty()) {
            markdown.append("## API分组\n\n");
            for (SwaggerDoc.Tag tag : tags) {
                markdown.append("- **").append(tag.getName()).append("**");
                if (!StringUtils.isEmpty(tag.getDescription())) {
                    markdown.append(": ").append(tag.getDescription());
                }
                markdown.append("\n");
            }
            markdown.append("\n");
        }
    }

    /**
     * 添加API路径
     */
    private void addPaths(StringBuilder markdown, SwaggerDoc swaggerDoc) {
        Map<String, Map<String, SwaggerDoc.Operation>> paths = swaggerDoc.getPaths();
        if (paths == null || paths.isEmpty()) {
            return;
        }

        markdown.append("## API接口\n\n");

        for (Map.Entry<String, Map<String, SwaggerDoc.Operation>> pathEntry : paths.entrySet()) {
            String path = pathEntry.getKey();
            Map<String, SwaggerDoc.Operation> operations = pathEntry.getValue();

            for (Map.Entry<String, SwaggerDoc.Operation> operationEntry : operations.entrySet()) {
                String method = operationEntry.getKey().toUpperCase();
                SwaggerDoc.Operation operation = operationEntry.getValue();

                addOperation(markdown, path, method, operation);
            }
        }
    }

    /**
     * 添加单个API操作
     */
    private void addOperation(StringBuilder markdown, String path, String method, SwaggerDoc.Operation operation) {
        // 接口标题
        markdown.append("### ").append(method).append(" ").append(path).append("\n\n");

        // 接口描述
        if (!StringUtils.isEmpty(operation.getSummary())) {
            markdown.append("**摘要**: ").append(operation.getSummary()).append("\n\n");
        }
        if (!StringUtils.isEmpty(operation.getDescription())) {
            markdown.append("**描述**: ").append(operation.getDescription()).append("\n\n");
        }

        // 标签
        if (operation.getTags() != null && !operation.getTags().isEmpty()) {
            markdown.append("**标签**: ").append(String.join(", ", operation.getTags())).append("\n\n");
        }

        // 参数
        addParameters(markdown, operation);

        // 响应
        addResponses(markdown, operation);

        markdown.append("---\n\n");
    }

    /**
     * 添加参数信息
     */
    private void addParameters(StringBuilder markdown, SwaggerDoc.Operation operation) {
        List<SwaggerDoc.Parameter> parameters = operation.getParameters();
        if (parameters == null || parameters.isEmpty()) {
            return;
        }

        markdown.append("**参数**:\n\n");
        markdown.append("| 参数名 | 位置 | 类型 | 必填 | 描述 |\n");
        markdown.append("|--------|------|------|------|------|\n");

        for (SwaggerDoc.Parameter param : parameters) {
            markdown.append("| ").append(param.getName() != null ? param.getName() : "")
                    .append(" | ").append(param.getIn() != null ? param.getIn() : "")
                    .append(" | ").append(getParameterType(param))
                    .append(" | ").append(param.isRequired() ? "是" : "否")
                    .append(" | ").append(param.getDescription() != null ? param.getDescription() : "")
                    .append(" |\n");
        }
        markdown.append("\n");
    }

    /**
     * 获取参数类型
     */
    private String getParameterType(SwaggerDoc.Parameter param) {
        // 处理schema引用类型（通常是body参数的POJO类）
        if (param.getSchema() != null && param.getSchema().getRef() != null) {
            return extractRefName(param.getSchema().getRef());
        }

        // 处理基本类型
        String type = param.getType() != null ? param.getType() : "";
        String format = param.getFormat();

        // 组合类型和格式
        if (format != null && !format.isEmpty()) {
            return type + "(" + format + ")";
        }

        return type;
    }

    /**
     * 添加响应信息
     */
    private void addResponses(StringBuilder markdown, SwaggerDoc.Operation operation) {
        Map<String, SwaggerDoc.Response> responses = operation.getResponses();
        if (responses == null || responses.isEmpty()) {
            return;
        }

        markdown.append("**响应**:\n\n");
        markdown.append("| 状态码 | 描述 | 数据类型 |\n");
        markdown.append("|--------|------|----------|\n");

        for (Map.Entry<String, SwaggerDoc.Response> responseEntry : responses.entrySet()) {
            String statusCode = responseEntry.getKey();
            SwaggerDoc.Response response = responseEntry.getValue();
            
            String dataType = "";
            if (response.getSchema() != null && response.getSchema().getRef() != null) {
                dataType = extractRefName(response.getSchema().getRef());
            }

            markdown.append("| ").append(statusCode)
                    .append(" | ").append(response.getDescription() != null ? response.getDescription() : "")
                    .append(" | ").append(dataType)
                    .append(" |\n");
        }
        markdown.append("\n");
    }

    /**
     * 提取引用名称
     */
    private String extractRefName(String ref) {
        if (ref != null && ref.startsWith("#/definitions/")) {
            return ref.substring("#/definitions/".length());
        }
        return ref;
    }

    /**
     * 添加数据模型定义
     */
    private void addDefinitions(StringBuilder markdown, SwaggerDoc swaggerDoc) {
        Map<String, SwaggerDoc.Definition> definitions = swaggerDoc.getDefinitions();
        if (definitions == null || definitions.isEmpty()) {
            return;
        }

        markdown.append("## 数据模型\n\n");

        for (Map.Entry<String, SwaggerDoc.Definition> defEntry : definitions.entrySet()) {
            String modelName = defEntry.getKey();
            SwaggerDoc.Definition definition = defEntry.getValue();

            addDefinition(markdown, modelName, definition);
        }
    }

    /**
     * 添加单个数据模型定义
     */
    private void addDefinition(StringBuilder markdown, String modelName, SwaggerDoc.Definition definition) {
        markdown.append("### ").append(modelName).append("\n\n");

        if (!StringUtils.isEmpty(definition.getDescription())) {
            markdown.append("**描述**: ").append(definition.getDescription()).append("\n\n");
        }

        Map<String, SwaggerDoc.Property> properties = definition.getProperties();
        if (properties != null && !properties.isEmpty()) {
            markdown.append("**属性**:\n\n");
            markdown.append("| 属性名 | 类型 | 描述 |\n");
            markdown.append("|--------|------|------|\n");

            for (Map.Entry<String, SwaggerDoc.Property> propEntry : properties.entrySet()) {
                String propName = propEntry.getKey();
                SwaggerDoc.Property property = propEntry.getValue();

                String propType = getPropertyType(property);
                String propDesc = property.getDescription() != null ? property.getDescription() : "";

                markdown.append("| ").append(propName)
                        .append(" | ").append(propType)
                        .append(" | ").append(propDesc)
                        .append(" |\n");
            }
            markdown.append("\n");
        }

        markdown.append("---\n\n");
    }

    /**
     * 获取属性类型
     */
    private String getPropertyType(SwaggerDoc.Property property) {
        if (property.getRef() != null) {
            return extractRefName(property.getRef());
        }
        
        String type = property.getType() != null ? property.getType() : "";
        if ("array".equals(type) && property.getItems() != null) {
            String itemType = getPropertyType(property.getItems());
            return "Array<" + itemType + ">";
        }
        
        if (property.getFormat() != null) {
            return type + "(" + property.getFormat() + ")";
        }
        
        return type;
    }

    /**
     * 保存到数据库
     */
    private void saveToDatabase(Long knowledgeGroupId, SwaggerDoc swaggerDoc, String markdown) {
        try {
            String title = "Swagger API文档";
            if (swaggerDoc.getInfo() != null && !StringUtils.isEmpty(swaggerDoc.getInfo().getTitle())) {
                title = swaggerDoc.getInfo().getTitle();
            }

            Knowledge knowledge = new Knowledge();
            knowledge.setSource(KnowledgeSource.SWAGGER.name());
            knowledge.setTargetName(title);
            knowledge.setTargetId(swaggerDoc.getHost() != null ? swaggerDoc.getHost() : "swagger-api");
            knowledge.setTargetStatus(KnowledgeTargetStatus.NORMAL.name());
            knowledge.setContent(markdown);
            knowledge.setExtendJson("");
            knowledge.setKnowledgeGroupId(knowledgeGroupId);
            knowledge.setUploadStatus(KnowledgeUploadStatus.SUCCESS.name());

            int insert = knowledgeRepository.insert(knowledge);
            if (insert > 0) {
                log.info("✅ 写入DB完成: {}", JsonUtils.toJson(knowledge));
            }
        } catch (Exception e) {
            log.error("❌ 保存到数据库失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存到文件
     */
    private void saveToFile(SwaggerDoc swaggerDoc, String markdown) {
        try {
            String fileName = "swagger-api-docs.md";
            if (swaggerDoc.getInfo() != null && !StringUtils.isEmpty(swaggerDoc.getInfo().getTitle())) {
                fileName = sanitizeFileName(swaggerDoc.getInfo().getTitle()) + ".md";
            }

            String filePath = outputDir + File.separator + fileName;

            try (OutputStreamWriter writer = new OutputStreamWriter(
                    new FileOutputStream(filePath), StandardCharsets.UTF_8)) {
                writer.write(markdown);
                log.info("✅ 文件保存完成: {}", fileName);
            }
        } catch (Exception e) {
            log.error("❌ 保存文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理文件名
     */
    private String sanitizeFileName(String fileName) {
        return fileName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_");
    }
}
