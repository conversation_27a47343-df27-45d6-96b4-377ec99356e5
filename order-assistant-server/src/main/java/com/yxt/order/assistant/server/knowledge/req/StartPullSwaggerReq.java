package com.yxt.order.assistant.server.knowledge.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 开始拉取Swagger文档请求参数
 */
@Data
public class StartPullSwaggerReq {

    /**
     * 知识库组ID
     */
    @NotNull(message = "知识库组ID不能为空")
    private Long knowledgeGroupId;

    /**
     * Swagger API文档URL
     */
    @NotBlank(message = "Swagger URL不能为空")
    private String swaggerUrl;
}
