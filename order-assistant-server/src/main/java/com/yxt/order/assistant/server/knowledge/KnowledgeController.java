package com.yxt.order.assistant.server.knowledge;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.req.StartPullCfReq;
import com.yxt.order.assistant.server.knowledge.req.StartPullSwaggerReq;
import com.yxt.order.assistant.server.knowledge.service.KnowledgeService;
import com.yxt.order.assistant.server.repository.KnowledgeGroupRepository;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/knowledge")
public class KnowledgeController extends AbstractController {

  @Resource
  private KnowledgeGroupRepository knowledgeGroupRepository;

  @Resource
  private KnowledgeService knowledgeService;


  /**
   * 获取所有支持库(订单助手自有的)
   *
   * @return
   */
  @PostMapping("/list-all-knowledge")
  public ResponseBase<List<KnowledgeGroup>> listAllKnowledge() {
    LambdaQueryWrapper<KnowledgeGroup> query = new LambdaQueryWrapper<>();
    return generateSuccess(knowledgeGroupRepository.selectList(query));
  }

  /**
   * 启动拉取cf知识库
   *
   * @return
   */
  @PostMapping("/start-pull-cf")
  public ResponseBase<String> startPullCf(@RequestBody StartPullCfReq req) {
    Long knowledgeGroupId = req.getKnowledgeGroupId();

    KnowledgeGroup knowledgeGroup = knowledgeService.detail(knowledgeGroupId);
    String extendJson = knowledgeGroup.getExtendJson();
    if (StringUtils.isEmpty(extendJson)) {
      throw new RuntimeException(String.format("%s 拓展配置不能为空", knowledgeGroup.getName()));
    }
    knowledgeService.pullCfKnowledge(knowledgeGroup);
    return generateSuccess("拉取成功");
  }


}
