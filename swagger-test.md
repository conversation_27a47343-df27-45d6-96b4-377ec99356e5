# Swagger转Markdown功能测试

## 功能说明

实现了将Swagger JSON数据转换为Markdown文件的功能，包括：

1. **SwaggerDoc数据模型类** - 用于解析Swagger JSON结构
2. **SwaggerMarkdownExporter** - 核心转换逻辑
3. **REST API接口** - 提供HTTP接口调用

## API接口

### POST /knowledge/start-pull-swagger

**请求参数**:
```json
{
  "knowledgeGroupId": 1,
  "swaggerUrl": "http://order-service.svc.k8s.test.hxyxt.com/v2/api-docs"
}
```

**响应**:
```json
{
  "success": true,
  "data": "Swagger文档拉取成功",
  "message": null
}
```

## 功能特性

1. **自动获取Swagger JSON** - 从指定URL获取API文档
2. **完整的Markdown转换** - 包括：
   - API基本信息
   - 标签分组
   - 接口详情（路径、方法、参数、响应）
   - 数据模型定义
3. **数据库存储** - 自动保存到知识库
4. **文件导出** - 可选择保存为本地文件

## 转换示例

原始Swagger JSON会被转换为结构化的Markdown文档，包含：

- # API文档标题
- ## 服务器信息
- ## API分组
- ## API接口
  - ### GET/POST /path
  - 参数表格
  - 响应表格
- ## 数据模型
  - 属性表格

## 使用方法

1. 确保知识库组已创建
2. 调用API接口，传入知识库组ID和Swagger URL
3. 系统自动拉取、转换并存储文档
4. 可通过知识库查看转换后的Markdown内容

## 技术实现

- 使用OkHttp进行HTTP请求
- 使用Gson进行JSON解析
- 支持Swagger 2.0格式
- 自动处理$ref引用
- 生成标准Markdown表格格式
